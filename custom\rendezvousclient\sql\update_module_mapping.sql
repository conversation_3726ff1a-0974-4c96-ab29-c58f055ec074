-- Mise à jour de la table des modules pour ajouter le mapping Dolibarr
-- Ajouter la colonne pour le nom du module Do<PERSON>barr si elle n'existe pas

ALTER TABLE llx_avimm_constante_module 
ADD COLUMN dolibarr_module_name VARCHAR(100) DEFAULT NULL COMMENT 'Nom du module Dolibarr correspondant';

-- Mise à jour des modules existants avec leur équivalent Dolibarr
UPDATE llx_avimm_constante_module SET dolibarr_module_name = 'SOCIETE' WHERE libelle LIKE '%Commercial%' OR libelle LIKE '%Tiers%' OR libelle LIKE '%CRM%';
UPDATE llx_avimm_constante_module SET dolibarr_module_name = 'FACTURE' WHERE libelle LIKE '%Factur%' OR libelle LIKE '%Comptab%';
UPDATE llx_avimm_constante_module SET dolibarr_module_name = 'STOCK' WHERE libelle LIKE '%Stock%' OR libelle LIKE '%Inventaire%';
UPDATE llx_avimm_constante_module SET dolibarr_module_name = 'PROJET' WHERE libelle LIKE '%Projet%';
UPDATE llx_avimm_constante_module SET dolibarr_module_name = 'ADHERENT' WHERE libelle LIKE '%RH%' OR libelle LIKE '%Adhér%' OR libelle LIKE '%Membre%';
UPDATE llx_avimm_constante_module SET dolibarr_module_name = 'BOUTIQUE' WHERE libelle LIKE '%E-commerce%' OR libelle LIKE '%Boutique%';
UPDATE llx_avimm_constante_module SET dolibarr_module_name = 'COMMANDE' WHERE libelle LIKE '%Commande%';
UPDATE llx_avimm_constante_module SET dolibarr_module_name = 'EXPEDITION' WHERE libelle LIKE '%Expédition%' OR libelle LIKE '%Livraison%';
UPDATE llx_avimm_constante_module SET dolibarr_module_name = 'CONTRAT' WHERE libelle LIKE '%Contrat%';
UPDATE llx_avimm_constante_module SET dolibarr_module_name = 'AGENDA' WHERE libelle LIKE '%Agenda%' OR libelle LIKE '%Calendrier%';

-- Insertion de modules de base si ils n'existent pas
INSERT IGNORE INTO llx_avimm_constante_module (libelle, dolibarr_module_name, fk_logiciel) VALUES
('Gestion Commerciale', 'SOCIETE', 1),
('Facturation', 'FACTURE', 1),
('Gestion de Stock', 'STOCK', 1),
('CRM', 'SOCIETE', 1),
('Projets', 'PROJET', 1),
('Commandes', 'COMMANDE', 1),
('Expéditions', 'EXPEDITION', 1),
('Contrats', 'CONTRAT', 1),
('Agenda', 'AGENDA', 1),
('Comptabilité', 'COMPTABILITE', 1),
('RH / Adhérents', 'ADHERENT', 2),
('E-commerce', 'BOUTIQUE', 2);

-- Insertion de constantes de base si elles n'existent pas
INSERT IGNORE INTO llx_avimm_constante (libelle, description) VALUES
('MAIN_MODULE_SOCIETE', 'Active le module Tiers/Sociétés'),
('MAIN_MODULE_FACTURE', 'Active le module Facturation'),
('MAIN_MODULE_STOCK', 'Active le module Gestion de Stock'),
('MAIN_MODULE_PROJET', 'Active le module Projets'),
('MAIN_MODULE_COMMANDE', 'Active le module Commandes'),
('MAIN_MODULE_EXPEDITION', 'Active le module Expéditions'),
('MAIN_MODULE_CONTRAT', 'Active le module Contrats'),
('MAIN_MODULE_AGENDA', 'Active le module Agenda'),
('MAIN_MODULE_COMPTABILITE', 'Active le module Comptabilité'),
('MAIN_MODULE_ADHERENT', 'Active le module Adhérents'),
('MAIN_MODULE_BOUTIQUE', 'Active le module Boutique en ligne'),
('SOCIETE_CODECLIENT_ADDON', 'Générateur de code client'),
('FACTURE_ADDON', 'Générateur de numérotation des factures'),
('STOCK_CALCULATE_ON_BILL', 'Calcul automatique du stock'),
('PROJET_ADDON', 'Générateur de référence projet'),
('MAIN_LANG_DEFAULT', 'Langue par défaut'),
('MAIN_SIZE_LISTE_LIMIT', 'Nombre d\'éléments par page'),
('MAIN_INFO_SOCIETE_NOM', 'Nom de la société'),
('MAIN_INFO_SOCIETE_ADRESSE', 'Adresse de la société'),
('MAIN_INFO_SOCIETE_CP', 'Code postal de la société'),
('MAIN_INFO_SOCIETE_VILLE', 'Ville de la société'),
('MAIN_INFO_SOCIETE_TEL', 'Téléphone de la société'),
('MAIN_INFO_SOCIETE_MAIL', 'Email de la société'),
('MAIN_INFO_SOCIETE_WEB', 'Site web de la société');

-- Liaison des constantes aux modules
INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_SOCIETE' AND m.dolibarr_module_name = 'SOCIETE';

INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_FACTURE' AND m.dolibarr_module_name = 'FACTURE';

INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_STOCK' AND m.dolibarr_module_name = 'STOCK';

INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_PROJET' AND m.dolibarr_module_name = 'PROJET';

INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_COMMANDE' AND m.dolibarr_module_name = 'COMMANDE';

INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_EXPEDITION' AND m.dolibarr_module_name = 'EXPEDITION';

INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_CONTRAT' AND m.dolibarr_module_name = 'CONTRAT';

INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_AGENDA' AND m.dolibarr_module_name = 'AGENDA';

INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_COMPTABILITE' AND m.dolibarr_module_name = 'COMPTABILITE';

INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_ADHERENT' AND m.dolibarr_module_name = 'ADHERENT';

INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) 
SELECT c.rowid, m.rowid 
FROM llx_avimm_constante c, llx_avimm_constante_module m 
WHERE c.libelle = 'MAIN_MODULE_BOUTIQUE' AND m.dolibarr_module_name = 'BOUTIQUE';
