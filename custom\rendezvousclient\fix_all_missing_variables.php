<?php
/**
 *	\file       rendezvousclient/fix_all_missing_variables.php
 *	\ingroup    rendezvousclient
 *	\brief      Correction automatique de toutes les variables manquantes
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'fix_all_variables') {
    $files_fixed = 0;
    $errors_fixed = 0;
    $results = array();
    
    // Liste de tous les fichiers à vérifier et corriger
    $files_to_check = array(
        // Module rendezvousclient
        'custom/rendezvousclient/rdv/list.php',
        'custom/rendezvousclient/rdv/card.php',
        'custom/rendezvousclient/site/list.php',
        'custom/rendezvousclient/site/card.php',
        'custom/rendezvousclient/cdc/list.php',
        'custom/rendezvousclient/cdc/card.php',
        'custom/rendezvousclient/demo/list.php',
        
        // Module constanteavimm (si présent)
        'custom/constanteavimm/logiciel/list.php',
        'custom/constanteavimm/logiciel/card.php',
        'custom/constanteavimm/constante/list.php',
        'custom/constanteavimm/constante/card.php',
        'custom/constanteavimm/module/list.php',
        'custom/constanteavimm/module/card.php',
        'custom/constanteavimm/extrafield/list.php'
    );
    
    foreach ($files_to_check as $file) {
        $full_path = DOL_DOCUMENT_ROOT.'/'.$file;
        
        if (file_exists($full_path)) {
            $content = file_get_contents($full_path);
            $original_content = $content;
            $file_errors_fixed = 0;
            
            // Variables communes à ajouter selon le type de fichier
            $variables_to_add = array();
            
            if (strpos($file, 'list.php') !== false) {
                // Variables pour les fichiers list.php
                $variables_to_add = array(
                    '$help_url = \'\';',
                    '$massactionbutton = \'\';',
                    '$massaction = GETPOST(\'massaction\', \'alpha\');',
                    '$arrayofselected = array();',
                    '$contextpage = \'\';',
                    '$socid = GETPOST(\'socid\', \'int\');',
                    '$newcardbutton = \'\';'
                );
            } elseif (strpos($file, 'card.php') !== false) {
                // Variables pour les fichiers card.php
                $variables_to_add = array(
                    '$help_url = \'\';',
                    '$backtopage = GETPOST(\'backtopage\', \'alpha\');',
                    '$lastkey = \'\';'
                );
            }
            
            // Vérifier et ajouter les variables manquantes
            foreach ($variables_to_add as $variable) {
                $var_name = explode(' = ', $variable)[0];
                
                // Vérifier si la variable n'existe pas déjà
                if (strpos($content, $var_name) === false) {
                    // Chercher où insérer la variable (après les GETPOST ou après $langs->loadLangs)
                    $patterns = array(
                        '/(\$langs->loadLangs\([^;]+;\s*)/m',
                        '/(\$action\s*=\s*GETPOST\([^;]+;\s*)/m',
                        '/(\$id\s*=\s*GETPOST\([^;]+;\s*)/m'
                    );
                    
                    $inserted = false;
                    foreach ($patterns as $pattern) {
                        if (preg_match($pattern, $content)) {
                            $content = preg_replace($pattern, '$1' . $variable . "\n", $content, 1);
                            $file_errors_fixed++;
                            $inserted = true;
                            break;
                        }
                    }
                    
                    // Si aucun pattern trouvé, insérer après require_once
                    if (!$inserted) {
                        $pattern = '/(require_once[^;]+;\s*)/m';
                        if (preg_match($pattern, $content)) {
                            $content = preg_replace($pattern, '$1' . $variable . "\n", $content, 1);
                            $file_errors_fixed++;
                        }
                    }
                }
            }
            
            // Fix spécifique : Ajouter le fix pour $user->projet
            if (strpos($content, 'if (!isset($user->projet))') === false) {
                $user_fix = "\n// Fix pour les propriétés manquantes de l'objet \$user\nif (!isset(\$user->projet)) {\n    \$user->projet = new stdClass();\n}\n";
                $pattern = '/(require_once[^;]+main\.inc\.php[^;]*;\s*)/m';
                if (preg_match($pattern, $content)) {
                    $content = preg_replace($pattern, '$1' . $user_fix, $content, 1);
                    $file_errors_fixed++;
                }
            }
            
            // Fix spécifique : Corriger les vérifications $arrayfields
            $arrayfields_patterns = array(
                '/\$arrayfields\[([^\]]+)\]\[\'checked\'\]/' => '!empty($arrayfields[$1][\'checked\'])'
            );
            
            foreach ($arrayfields_patterns as $search => $replace) {
                if (preg_match($search, $content) && strpos($content, '!empty($arrayfields') === false) {
                    $content = preg_replace($search, $replace, $content);
                    $file_errors_fixed++;
                }
            }
            
            // Sauvegarder le fichier si des modifications ont été apportées
            if ($file_errors_fixed > 0 && $content !== $original_content) {
                file_put_contents($full_path, $content);
                $files_fixed++;
                $errors_fixed += $file_errors_fixed;
                
                $results[] = array(
                    'file' => $file,
                    'status' => 'CORRIGÉ',
                    'errors_fixed' => $file_errors_fixed
                );
            } else {
                $results[] = array(
                    'file' => $file,
                    'status' => 'OK',
                    'errors_fixed' => 0
                );
            }
        } else {
            $results[] = array(
                'file' => $file,
                'status' => 'NON TROUVÉ',
                'errors_fixed' => 0
            );
        }
    }
    
    if ($files_fixed > 0) {
        setEventMessages("$files_fixed fichier(s) corrigé(s) avec $errors_fixed erreur(s) résolue(s)", null, 'mesgs');
    } else {
        setEventMessages("Aucune correction nécessaire", null, 'mesgs');
    }
}

/*
 * View
 */

$title = 'Correction de toutes les variables manquantes';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script corrige automatiquement toutes les variables manquantes dans tous les fichiers du module.';
print '</div>';

if ($action != 'fix_all_variables') {
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=fix_all_variables&token='.newToken().'">Corriger toutes les variables manquantes</a>';
    print '</div>';
    
    print '<br><h3>Variables qui seront ajoutées</h3>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Type de fichier</th>';
    print '<th>Variables ajoutées</th>';
    print '<th>Description</th>';
    print '</tr>';
    
    $variables_info = array(
        'list.php' => array(
            'variables' => array('$help_url', '$massactionbutton', '$massaction', '$arrayofselected', '$contextpage', '$socid', '$newcardbutton'),
            'description' => 'Variables nécessaires pour les listes avec actions de masse et filtres'
        ),
        'card.php' => array(
            'variables' => array('$help_url', '$backtopage', '$lastkey'),
            'description' => 'Variables nécessaires pour les formulaires de création/modification'
        ),
        'Tous les fichiers' => array(
            'variables' => array('$user->projet fix'),
            'description' => 'Fix pour éviter les erreurs dans security.lib.php'
        )
    );
    
    foreach ($variables_info as $type => $info) {
        print '<tr class="oddeven">';
        print '<td><strong>'.$type.'</strong></td>';
        print '<td>'.implode(', ', $info['variables']).'</td>';
        print '<td>'.$info['description'].'</td>';
        print '</tr>';
    }
    
    print '</table>';
    
    print '<br><h3>Corrections qui seront appliquées</h3>';
    print '<div class="ok">';
    print '<ul>';
    print '<li><strong>Variables manquantes :</strong> Ajout automatique selon le type de fichier</li>';
    print '<li><strong>Fix $user->projet :</strong> Ajout dans tous les fichiers</li>';
    print '<li><strong>Vérifications $arrayfields :</strong> Ajout de !empty() si nécessaire</li>';
    print '<li><strong>Placement intelligent :</strong> Variables ajoutées au bon endroit dans le code</li>';
    print '</ul>';
    print '</div>';
    
} else {
    print '<h3>Résultats de la correction automatique</h3>';
    
    if (!empty($results)) {
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Fichier</th>';
        print '<th>Statut</th>';
        print '<th>Erreurs corrigées</th>';
        print '</tr>';
        
        $nb_corrected = 0;
        $nb_ok = 0;
        $nb_not_found = 0;
        
        foreach ($results as $result) {
            print '<tr class="oddeven">';
            print '<td>'.basename($result['file']).'</td>';
            print '<td>';
            switch ($result['status']) {
                case 'CORRIGÉ':
                    print '<span class="badge badge-status4 badge-status">CORRIGÉ</span>';
                    $nb_corrected++;
                    break;
                case 'OK':
                    print '<span class="badge badge-status1 badge-status">OK</span>';
                    $nb_ok++;
                    break;
                case 'NON TROUVÉ':
                    print '<span class="badge badge-status8 badge-status">NON TROUVÉ</span>';
                    $nb_not_found++;
                    break;
            }
            print '</td>';
            print '<td>'.$result['errors_fixed'].'</td>';
            print '</tr>';
        }
        
        print '</table>';
        
        print '<br>';
        if ($nb_corrected > 0) {
            print '<div class="ok">';
            print '<strong>Succès !</strong> '.$nb_corrected.' fichier(s) corrigé(s), '.$nb_ok.' fichier(s) déjà OK.';
            print '</div>';
        } else {
            print '<div class="info">';
            print '<strong>Aucune correction nécessaire.</strong> Tous les fichiers sont déjà corrects.';
            print '</div>';
        }
        
        if ($nb_not_found > 0) {
            print '<div class="warning">';
            print '<strong>Note :</strong> '.$nb_not_found.' fichier(s) non trouvé(s) (normal si certains modules ne sont pas installés).';
            print '</div>';
        }
    }
    
    print '<div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
    print '</div>';
}

// Test recommandé après correction
print '<br><h3>Test recommandé</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_final_fix.php">Lancer le test final complet</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_site_class.php">Test classe Site</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_rendezvous_class.php">Test classe Rendezvous</a>';
print '</div>';

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rdv/list.php">Liste des rendez-vous</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/cdc/list.php">Liste des CDC</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
