<?php
/**
 *	\file       rendezvousclient/install.php
 *	\ingroup    rendezvousclient
 *	\brief      Script d'installation du module rendezvousclient
 */

require_once '../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

$action = GETPOST('action', 'az09');

/*
 * Actions
*/

if($action == 'install'){
    $error = 0;

    // Lire et exécuter le script SQL principal
    $sqlfile = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/sql/dolibarr_allversions.sql';

    if (file_exists($sqlfile)) {
        $sql_content = file_get_contents($sqlfile);

        // Diviser le contenu en requêtes individuelles
        $sql_queries = explode(';', $sql_content);

        foreach ($sql_queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^--/', $query)) {
                $resql = $db->query($query);
                if (!$resql) {
                    $error++;
                    setEventMessages($langs->trans("ErrorCreatingTable").": ".$db->lasterror(), null, 'errors');
                }
            }
        }
    } else {
        $error++;
        setEventMessages("Fichier SQL non trouvé: ".$sqlfile, null, 'errors');
    }

    if($error == 0){
        setEventMessages($langs->trans("InstallationCompleted"), null, 'mesgs');
    }
}

/*
 * View
*/

llxHeader('', $langs->trans("InstallModule"));

print load_fiche_titre($langs->trans("InstallModule"), '', 'setup');

print '<div class="info">';
print $langs->trans("InstallModuleDescription");
print '</div>';

print '<form method="post" action="'.$_SERVER['PHP_SELF'].'">';
print '<input type="hidden" name="action" value="install">';
print '<input type="hidden" name="token" value="'.newToken().'">';

print '<div class="center">';
print '<input type="submit" class="button" value="'.$langs->trans("Install").'">';
print '</div>';

print '</form>';

// Vérifier l'état des tables
print '<br><h3>'.$langs->trans("TablesStatus").'</h3>';

$tables_to_check = array(
    'rendez_vous_synthese_cdc',
    'rendez_vous_demo'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>'.$langs->trans("Table").'</th>';
print '<th>'.$langs->trans("Status").'</th>';
print '</tr>';

foreach($tables_to_check as $table){
    $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
    $resql = $db->query($sql);

    print '<tr>';
    print '<td>'.MAIN_DB_PREFIX.$table.'</td>';

    if($resql && $db->num_rows($resql) > 0){
        print '<td><span class="badge badge-status4 badge-status">'.$langs->trans("Exists").'</span></td>';
    } else {
        print '<td><span class="badge badge-status8 badge-status">'.$langs->trans("NotExists").'</span></td>';
    }

    print '</tr>';
}

print '</table>';

// End of page
llxFooter();
$db->close();
