<?php
/*
 *  \file       constanteavimm/constante/class/constante.class.php
 *  \ingroup    constanteavimm
 *  \brief      Fichier des classes de constante
*/


class Constante
{
    /**
	 * @var string ID to identify managed object
	 */
	public $element = 'avimm_constante';

	/**
	 * @var string Name of table without prefix where object is stored
	 */
	public $table_element = 'avimm_constante';

    public $picto = 'tools';

	public $db;

	public $error;

    public $id;

    public $libelle;

	public $valeur;

	public $dependance_sql;

	public $description;

	public $debase;

	public $fk_logiciel;

	public $logiciel;

	public $modules = array();
	public $fk_modules = array();

	public $extrafields = array();
	public $fk_extrafields = array();

	public $parents_constantes = array();

	public $fichiers = array();
	public $fk_fichiers = array();

	public $cahier_des_charges;
	
	public $tarif;

    /**
	 *	Constructor
	 *
	 *  @param		DoliDB		$db      Database handler
	 */
	public function __construct($db)
	{
		$this->db = $db;
	}

    /**
	 *	Create constante
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function create()
	{
		global $conf, $langs;

        $this->db->begin();
		
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante(libelle, valeur, dependance_sql, description, debase, cahier_des_charges, tarif)";
        $sql .= " VALUES(";
		$sql .= " '".$this->db->escape($this->libelle)."'";
		$sql .= ", '".$this->db->escape($this->valeur)."'";
		$sql .= ", '".$this->db->escape($this->dependance_sql)."'";
		$sql .= ", '".$this->db->escape($this->description)."'";
		$sql .= ", ".$this->debase."";
		$sql .= ", '".$this->db->escape($this->cahier_des_charges)."'";
		$sql .= ", '".price2num($this->tarif, 'MT')."'";
        $sql .= ")";
		
        $resql = $this->db->query($sql);
		if($resql){
			$this->id = $this->db->last_insert_id(MAIN_DB_PREFIX.'avimm_constante');

			if ($this->id) {
                $this->fetch($this->id);
				
				$this->setModules($this->fk_modules);
				// $this->setExtrafields($this->fk_extrafields);
				$this->setConstantesParents($this->parents_constantes);
				$this->setFichiers($this->fk_fichiers);

                $this->db->commit();
                return 1;
            }
        }else{
			dol_print_error($this->db);
			$this->db->rollback();
			return -1;
		}
    }

    /**
	 *	Fetch constante
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function fetch($id)
	{
		// Check parameters
		if(empty($id)){
			return -1;
		}

        $sql = "SELECT ac.rowid as const_id, ac.libelle as libelle, ac.valeur, ac.dependance_sql, ac.description, ac.debase, ac.cahier_des_charges, ac.tarif, GROUP_CONCAT(acm.libelle SEPARATOR '<br/>') as module, acl.rowid as fk_logiciel, acl.libelle as logiciel";
        $sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante ac";
		$sql .= " LEFT OUTER JOIN ".MAIN_DB_PREFIX."avimm_constante_inmodule acim ON acim.fk_constante = ac.rowid";
		$sql .= " LEFT OUTER JOIN ".MAIN_DB_PREFIX."avimm_constante_module acm ON acm.rowid = acim.fk_module";
		$sql .= " LEFT OUTER JOIN ".MAIN_DB_PREFIX."avimm_constante_logiciel acl ON acl.rowid = acm.fk_logiciel";
        $sql .= " WHERE ac.rowid = ".$id;
		$sql .= " GROUP BY ac.rowid";

        $resql = $this->db->query($sql);
		if($resql){
			$obj = $this->db->fetch_object($resql);

            if($obj){
                $this->id = $obj->const_id;
				$this->libelle = $obj->libelle;
                $this->valeur = $obj->valeur;
                $this->dependance_sql = $obj->dependance_sql;
                $this->description = $obj->description;
                $this->debase = $obj->debase;
                $this->fk_logiciel = $obj->fk_logiciel;
                $this->logiciel = $obj->logiciel;
                $this->cahier_des_charges = $obj->cahier_des_charges;
                $this->tarif = $obj->tarif;

				$this->fetch_module($id);
				$this->fetch_extrafields($id);
				$this->fetch_parent_constante($id);
				$this->fetch_fichiers($id);

				return 1;
            }else {
				$this->error = 'Constante with id '.$id.' not found sql='.$sql;
				return 0;
			}

        }else{
			$this->error = $this->db->error();
			return -1;
		}
    }

	/**
	 *	Fetch module of constante
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function fetch_module($id)
	{
		require_once DOL_DOCUMENT_ROOT.'/custom/constanteavimm/module/class/module.class.php';

		// Check parameters
		if(empty($id)){
			return -1;
		}

		$sql = "SELECT acm.rowid";
		$sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante ac";
		$sql .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_inmodule acim ON acim.fk_constante = ac.rowid";
		$sql .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_module acm ON acm.rowid = acim.fk_module";
		$sql .= " WHERE ac.rowid = ".$id;
		$sql .= " ORDER BY acm.rowid ASC";

		$resql = $this->db->query($sql);
		if($resql){
			$num = $this->db->num_rows($resql);
			$i = 0;

			while($i < $num){
				$obj = $this->db->fetch_object($resql);
				
				$moduletmp = new Module($this->db);
				$moduletmp->fetch($obj->rowid);

				$this->modules[$i] = $moduletmp;
				$this->fk_modules[$i] = $obj->rowid;

				$i++;
			}

            return 1;
		}else{
			return 0;
		}
	}

	/**
	 *	Fetch extrafields of constante
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function fetch_extrafields($id)
	{
		// Check parameters
		if(empty($id)){
			return -1;
		}

		$sql = "SELECT ace.rowid, ace.libelle, ace.type, ace.visible, ace.valeur, ace.objorline, ace.editable, ace.affpdf";
		$sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante ac";
		$sql .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_constextrafields acce ON acce.fk_constante = ac.rowid";
		$sql .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_extrafields ace ON ace.rowid = acce.fk_extrafield";
		$sql .= " WHERE ac.rowid = ".$id;
		$sql .= " ORDER BY ace.rowid ASC";

		$resql = $this->db->query($sql);
		if($resql){
			$num = $this->db->num_rows($resql);
			$i = 0;

			while($i < $num){
				$obj = $this->db->fetch_object($resql);

				$this->extrafields[$i]->rowid = $obj->rowid;
				$this->extrafields[$i]->libelle = $obj->libelle;
				$this->extrafields[$i]->type = $obj->type;
				$this->extrafields[$i]->visible = $obj->visible;
				$this->extrafields[$i]->valeur = $obj->valeur;
				$this->extrafields[$i]->objorline = $obj->objorline;
				$this->extrafields[$i]->editable = $obj->editable;
				$this->extrafields[$i]->affpdf = $obj->affpdf;
				
				$this->fk_extrafields[$i] = $obj->rowid;

				$i++;
			}

            return 1;
		}else{
			return 0;
		}
	}

	/**
	 *	Fetch parent of constante
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function fetch_parent_constante($id)
	{
		// Check parameters
		if(empty($id)){
			return -1;
		}

		$sql = "SELECT acp.fk_constante_parent";
		$sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante ac";
		$sql .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_parent acp ON ac.rowid = acp.fk_constante_enfant";
		$sql .= " WHERE ac.rowid = ".$id;
		$sql .= " ORDER BY acp.fk_constante_parent ASC";

		$resql = $this->db->query($sql);
		if($resql){
			$num = $this->db->num_rows($resql);
			$i = 0;

			while($i < $num){
				$obj = $this->db->fetch_object($resql);

				$this->parents_constantes[$i] = $obj->fk_constante_parent;

				$i++;
			}

            return 1;
		}else{
			return 0;
		}
	}

	/**
	 *	Fetch files of constante
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function fetch_fichiers($id)
	{
		// Check parameters
		if(empty($id)){
			return -1;
		}

		$sql = "SELECT acf.rowid, acf.fichier";
		$sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante ac";
		$sql .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_infic aci ON aci.fk_constante = ac.rowid";
		$sql .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_fichier acf ON acf.rowid = aci.fk_fichier";
		$sql .= " WHERE ac.rowid = ".$id;
		$sql .= " ORDER BY acf.rowid ASC";

		$resql = $this->db->query($sql);
		if($resql){
			$num = $this->db->num_rows($resql);
			$i = 0;

			while($i < $num){
				$obj = $this->db->fetch_object($resql);

				$this->fichiers[$i]->rowid = $obj->rowid;
				$this->fichiers[$i]->fichier = $obj->fichier;
				
				$this->fk_fichiers[$i] = $obj->rowid;

				$i++;
			}

            return 1;
		}else{
			return 0;
		}
	}

	/**
	 *	Update constante
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function update($id)
	{
		// Check parameters
		if(empty($id)){
			return -1;
		}

		$tmp = new Constante($this->db);
		$tmp->fetch($this->id);
		
		$this->db->begin();

		$sql = "UPDATE ".MAIN_DB_PREFIX."avimm_constante SET";
		$sql .= " libelle = '".$this->db->escape($this->libelle)."'";
		$sql .= ", valeur = '".$this->db->escape($this->valeur)."'";
		$sql .= ", dependance_sql = '".$this->db->escape($this->dependance_sql)."'";
		$sql .= ", description = '".$this->db->escape($this->description)."'";
		$sql .= ", debase = '".$this->debase."'";
		$sql .= ", cahier_des_charges = '".$this->db->escape($this->cahier_des_charges)."'";
		$sql .= ", tarif = '".price2num($this->tarif, 'MT')."'";
		$sql .= " WHERE rowid = ".$id;

		$resql = $this->db->query($sql);
		if($resql){
			$this->setModules($this->fk_modules, $tmp->fk_modules);
			$this->setExtrafields($this->fk_extrafields, $tmp->fk_extrafields);
			$this->setConstantesParents($this->parents_constantes, $tmp->parents_constantes);
			$this->setFichiers($this->fk_fichiers, $tmp->fk_fichiers);

			$this->db->commit();
			return 1;
		}else{
			$this->error = $this->db->error();
			$this->db->rollback();
			return -1;
		}
	}

	/**
	 *	Delete constante
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
    public function delete($id)
	{
        // Check parameters
		if(empty($id)){
			return -1;
		}

        $this->db->begin();

		$sql = "DELETE FROM ".MAIN_DB_PREFIX."avimm_constante_infic WHERE fk_constante = ".$id;

		$resql = $this->db->query($sql);
		if(!$resql){
			$this->error = $this->db->error();
            $this->db->rollback();
			return -1;
        }

		$sql = "DELETE FROM ".MAIN_DB_PREFIX."avimm_constante_parent WHERE fk_constante_enfant = ".$id;

		$resql = $this->db->query($sql);
		if(!$resql){
			$this->error = $this->db->error();
            $this->db->rollback();
			return -1;
        }

		$sql = "DELETE FROM ".MAIN_DB_PREFIX."avimm_constante_constextrafields WHERE fk_constante = ".$id;

		$resql = $this->db->query($sql);
		if(!$resql){
			$this->error = $this->db->error();
            $this->db->rollback();
			return -1;
        }

		$sql = "DELETE FROM ".MAIN_DB_PREFIX."avimm_constante_inmodule WHERE fk_constante = ".$id;

		$resql = $this->db->query($sql);
		if(!$resql){
			$this->error = $this->db->error();
            $this->db->rollback();
			return -1;
        }

        $sql = "DELETE FROM ".MAIN_DB_PREFIX."avimm_constante WHERE rowid = ".$id;
        
        $resql = $this->db->query($sql);
        if($resql){
            $this->db->commit();
            
            return 1;
        }else{
			$this->error = $this->db->error();
            $this->db->rollback();
			return -1;
        }
    }

    public function getNomUrl()
	{
        $url = DOL_URL_ROOT.'/custom/constanteavimm/constante/card.php?id='.$this->id;

        $link = '<a href="'.$url.'">';
        $link .= $this->libelle;
        $link .= '</a>';

        return $link;
    }

    public function listeLogiciel($selected, $name, $show_empty = 1)
	{
		global $form;

		$sql = "SELECT rowid, libelle";
		$sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante_logiciel";
		$sql .= " ORDER BY rowid ASC";

		$resql = $this->db->query($sql);
		if($resql->num_rows > 0){
			$array = array();
			foreach($resql as $line){
				$array[$line['rowid']] = $line['libelle'];
			}

			return $form->selectarray($name, $array, $selected, $show_empty);
		}else{
			$out = 'Aucun logiciel';
			return $out;
		}
    }

	public function listeModule($selected, $name, $multiselect = 0, $width = 600, $size = '')
	{
		global $form;

		$sql = "SELECT acm.rowid, acm.libelle, acl.libelle as logiciel";
		$sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante_module acm";
		$sql .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_logiciel acl ON acl.rowid = acm.fk_logiciel";
		$sql .= " ORDER BY acm.rowid ASC";

		$resql = $this->db->query($sql);
		if($resql->num_rows > 0){
			$array = array();
			foreach($resql as $line){
				$array[$line['rowid']] = $line['libelle'].' - '.$line['logiciel'];
			}
			
			if($multiselect == 1){
				return $form->multiselectarray($name, $array, $selected, 0, 0, '', 0, $width);
			}else{
				return $form->selectarray($name, $array, $selected, 1, 0, 0, '', 0, $size);
			}
		}else{
			$out = 'Aucun module';
			return $out;
		}
    }

	public function listeConstante($selected, $name, $multiselect = 0, $width = 600)
	{
		global $form;

		$sql = "SELECT rowid, libelle";
		$sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante";
		$sql .= " ORDER BY rowid ASC";

		$resql = $this->db->query($sql);
		if($resql->num_rows > 0){
			$array = array();
			foreach($resql as $line){
				$array[$line['rowid']] = $line['libelle'];
			}
			
			if($multiselect == 1){
				return $form->multiselectarray($name, $array, $selected, 0, 0, '', 0, $width);
			}else{
				return $form->selectarray($name, $array, $selected, 1);
			}
		}else{
			$out = 'Aucune constante';
			return $out;
		}
    }

	public function listeExtrafields($selected, $name, $multiselect = 0, $width = 600)
	{
		global $form;

		$sql = "SELECT ace.rowid, ace.libelle, acm.libelle as module";
		$sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante_extrafields ace";
		$sql .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_module acm ON acm.rowid = ace.fk_module";
		$sql .= " WHERE ace.fk_module IN (".implode(',', $this->fk_modules).")";
		$sql .= " ORDER BY ace.rowid ASC";
		
		$resql = $this->db->query($sql);
		if($resql->num_rows > 0){
			$array = array();
			foreach($resql as $line){
				$array[$line['rowid']] = $line['libelle'].' - '.$line['module'];
			}
			
			if($multiselect == 1){
				return $form->multiselectarray($name, $array, $selected, 0, 0, '', 0, $width);
			}else{
				return $form->selectarray($name, $array, $selected, 1);
			}
		}else{
			$out = 'Aucune constante';
			return $out;
		}
    }

	public function listeFichiers($selected, $name, $multiselect = 0, $width = 600)
	{
		global $form;

		$sql = "SELECT acf.rowid, acf.fichier";
		$sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante_fichier acf";
		$sql .= " ORDER BY acf.rowid ASC";
		
		$resql = $this->db->query($sql);
		if($resql->num_rows > 0){
			$array = array();
			foreach($resql as $line){
				$array[$line['rowid']] = $line['fichier'];
			}
			
			if($multiselect == 1){
				return $form->multiselectarray($name, $array, $selected, 0, 0, '', 0, $width);
			}else{
				return $form->selectarray($name, $array, $selected, 1);
			}
		}else{
			$out = 'Aucune constante';
			return $out;
		}
	}

	public function setModules($newModules, $oldModules = array())
	{
		if(!empty($oldModules)){
			// si $oldModules n'est pas vide c'est qu'on en mode update
			// comparer les 2 tableau
			
			// ceux de oldModules qui ne sont pas dans newModules on supprime
			$to_del = array_diff($oldModules, $newModules);
			// ceux de newModules qui ne sont pas dans oldModules on insert
			$to_add = array_diff($newModules, $oldModules);

			if($to_del){
				foreach($to_del as $fk_module){
					$sql = "DELETE FROM ".MAIN_DB_PREFIX."avimm_constante_inmodule WHERE fk_module = ".$fk_module." AND fk_constante = ".$this->id;
					$resql = $this->db->query($sql);
				}
			}

			if($to_add){
				foreach($to_add as $fk_module){
					$sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_inmodule(fk_constante, fk_module)";
					$sql .= " VALUES(".$this->id.", ".$fk_module.")";
					$resql = $this->db->query($sql);
				}
			}
		}elseif($newModules){
			// sinon mode create, on ajoute directement
			foreach($newModules as $fk_module){
				$sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_inmodule(fk_constante, fk_module)";
				$sql .= " VALUES(".$this->id.", ".$fk_module.")";
				$resql = $this->db->query($sql);
			}
		}
	}

	public function setExtrafields($newExtrafields, $oldExtrafields = array())
	{
		if(!empty($oldExtrafields)){
			// si $oldExtrafields n'est pas vide c'est qu'on en mode update
			// comparer les 2 tableau

			// ceux de oldExtrafields qui ne sont pas dans newExtrafields on supprime
			$to_del = array_diff($oldExtrafields, $newExtrafields);
			// ceux de newExtrafields qui ne sont pas dans oldExtrafields on insert
			$to_add = array_diff($newExtrafields, $oldExtrafields);

			if($to_del){
				foreach($to_del as $fk_extrafield){
					$sql = "DELETE FROM ".MAIN_DB_PREFIX."avimm_constante_constextrafields WHERE fk_extrafield = ".$fk_extrafield." AND fk_constante = ".$this->id;
					$resql = $this->db->query($sql);
				}
			}

			if($to_add){
				foreach($to_add as $fk_extrafield){
					$sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_constextrafields(fk_constante, fk_extrafield)";
					$sql .= " VALUES(".$this->id.", ".$fk_extrafield.")";
					$resql = $this->db->query($sql);
				}
			}
		}elseif($newExtrafields){
			// sinon mode create, on ajoute directement
			foreach($newExtrafields as $extrafieldid){
				$sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_constextrafields(fk_constante, fk_extrafield)";
				$sql .= " VALUES(".$this->id.", ".$extrafieldid.")";
				$resql = $this->db->query($sql);
			}
		}
	}

	public function setConstantesParents($newConstanteParents, $oldConstanteParents = array())
	{
		if(!empty($oldConstanteParents)){
			// si $oldConstanteParents n'est pas vide c'est qu'on en mode update
			// comparer les 2 tableau

			// ceux de oldConstanteParents qui ne sont pas dans newConstanteParents on supprime
			$to_del = array_diff($oldConstanteParents, $newConstanteParents);
			// ceux de newConstanteParents qui ne sont pas dans oldConstanteParents on insert
			$to_add = array_diff($newConstanteParents, $oldConstanteParents);

			if($to_del){
				foreach($to_del as $fk_constante_parent){
					$sql = "DELETE FROM ".MAIN_DB_PREFIX."avimm_constante_parent WHERE fk_constante_parent = ".$fk_constante_parent." AND fk_constante_enfant = ".$this->id;
					$resql = $this->db->query($sql);
				}
			}

			if($to_add){
				foreach($to_add as $fk_constante_parent){
					$sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_parent(fk_constante_enfant, fk_constante_parent)";
					$sql .= " VALUES(".$this->id.", ".$fk_constante_parent.")";
					$resql = $this->db->query($sql);
				}
			}
		}elseif($newConstanteParents){
			// sinon mode create, on ajoute directement
			foreach($newConstanteParents as $constanteid){
				$sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_parent(fk_constante_enfant, fk_constante_parent)";
				$sql .= " VALUES(".$this->id.", ".$constanteid.")";
				$resql = $this->db->query($sql);
			}
		}
	}

	public function setFichiers($newFichiers, $oldFichiers = array())
	{
		if(!empty($oldFichiers)){
			// si $oldFichiers n'est pas vide c'est qu'on en mode update
			// comparer les 2 tableau

			// ceux de oldFichiers qui ne sont pas dans newFichiers on supprime
			$to_del = array_diff($oldFichiers, $newFichiers);
			// ceux de newFichiers qui ne sont pas dans oldFichiers on insert
			$to_add = array_diff($newFichiers, $oldFichiers);

			if($to_del){
				foreach($to_del as $fk_fichier){
					$sql = "DELETE FROM ".MAIN_DB_PREFIX."avimm_constante_infic WHERE fk_fichier = ".$fk_fichier." AND fk_constante = ".$this->id;
					$resql = $this->db->query($sql);
				}
			}

			if($to_add){
				foreach($to_add as $fk_fichier){
					$sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_infic(fk_constante, fk_fichier)";
					$sql .= " VALUES(".$this->id.", ".$fk_fichier.")";
					$resql = $this->db->query($sql);
				}
			}
		}elseif($newFichiers){
			// sinon mode create, on ajoute directement
			foreach($newFichiers as $fk_fichier){
				$sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_infic(fk_constante, fk_fichier)";
				$sql .= " VALUES(".$this->id.", ".$fk_fichier.")";
				$resql = $this->db->query($sql);
			}
		}
	}
}