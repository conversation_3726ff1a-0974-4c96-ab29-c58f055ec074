<?php
/**
 *	\file       rendezvousclient/core/class/document_generator.class.php
 *	\ingroup    rendezvousclient
 *	\brief      Générateur de documents ODT
 */

require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/pdf.lib.php';

/**
 * Classe pour générer des documents ODT
 */
class DocumentGenerator
{
    public $db;
    public $error;
    public $errors = array();
    
    /**
     * Constructor
     */
    public function __construct($db)
    {
        $this->db = $db;
    }
    
    /**
     * Génère un document ODT pour un site
     */
    public function generateSiteDocument($site, $type = 'cahier_charges')
    {
        global $conf, $langs, $user;
        
        // Charger les données du site
        if (!is_object($site)) {
            $this->error = "Objet site invalide";
            return false;
        }
        
        // Charger les données associées
        $societe = new Societe($this->db);
        $projet = new Project($this->db);
        
        if ($site->fk_projet > 0) {
            $projet->fetch($site->fk_projet);
            if ($projet->socid > 0) {
                $societe->fetch($projet->socid);
            }
        }
        
        // Définir le template selon le type
        $template_file = $this->getTemplateFile($type);
        if (!$template_file) {
            $this->error = "Template non trouvé pour le type: $type";
            return false;
        }
        
        // Préparer les données de substitution
        $substitutions = $this->prepareSubstitutions($site, $societe, $projet);
        
        // Générer le document
        $output_file = $this->generateDocument($template_file, $substitutions, $site, $type);
        
        return $output_file;
    }
    
    /**
     * Récupère le fichier template selon le type
     */
    private function getTemplateFile($type)
    {
        $templates_dir = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/';
        
        $templates = array(
            'cahier_charges' => 'cahier_des_charges_template.odt',
            'proposition_technique' => 'proposition_technique_template.odt',
            'devis' => 'devis_template.odt',
            'synthese_cdc' => 'synthese_cdc_template.odt'
        );
        
        if (isset($templates[$type])) {
            $template_path = $templates_dir . $templates[$type];
            if (file_exists($template_path)) {
                return $template_path;
            }
        }
        
        return false;
    }
    
    /**
     * Prépare les substitutions pour le template
     */
    private function prepareSubstitutions($site, $societe, $projet)
    {
        global $conf, $user;
        
        $substitutions = array();
        
        // Informations de base
        $substitutions['__DATE_GENERATION__'] = dol_print_date(dol_now(), 'day');
        $substitutions['__DATE_GENERATION_COMPLETE__'] = dol_print_date(dol_now(), 'dayhour');
        $substitutions['__UTILISATEUR__'] = $user->getFullName($langs);
        
        // Informations société
        $substitutions['__SOCIETE_NOM__'] = $societe->name ?: '';
        $substitutions['__SOCIETE_ADRESSE__'] = $societe->address ?: '';
        $substitutions['__SOCIETE_CP__'] = $societe->zip ?: '';
        $substitutions['__SOCIETE_VILLE__'] = $societe->town ?: '';
        $substitutions['__SOCIETE_PAYS__'] = $societe->country ?: '';
        $substitutions['__SOCIETE_TEL__'] = $societe->phone ?: '';
        $substitutions['__SOCIETE_EMAIL__'] = $societe->email ?: '';
        $substitutions['__SOCIETE_SIRET__'] = $societe->idprof2 ?: '';
        
        // Informations projet
        $substitutions['__PROJET_REF__'] = $projet->ref ?: '';
        $substitutions['__PROJET_TITRE__'] = $projet->title ?: '';
        $substitutions['__PROJET_DESCRIPTION__'] = $projet->description ?: '';
        $substitutions['__PROJET_DATE_DEBUT__'] = $projet->date_start ? dol_print_date($projet->date_start, 'day') : '';
        $substitutions['__PROJET_DATE_FIN__'] = $projet->date_end ? dol_print_date($projet->date_end, 'day') : '';
        
        // Informations site
        $substitutions['__SITE_NOM__'] = $site->nom ?: '';
        $substitutions['__SITE_TYPE__'] = $site->type ?: '';
        $substitutions['__SITE_DESCRIPTION__'] = $site->description ?: '';
        $substitutions['__SITE_NB_UTILISATEURS__'] = $site->nombre_utilisateur ?: '0';
        $substitutions['__SITE_LOGICIEL__'] = $site->getLibLogiciel($site->fk_logiciel) ?: '';
        $substitutions['__SITE_AUTRE__'] = $site->autre ?: '';
        $substitutions['__SITE_HEBERGEMENT__'] = $site->hebergement ?: '';
        $substitutions['__SITE_DATE__'] = $site->date ? dol_print_date($db->jdate($site->date), 'day') : '';
        
        // Informations utilisateurs du site
        $site->fetchUtilisateur();
        $utilisateurs_text = '';
        if (!empty($site->lines_utilisateur)) {
            foreach ($site->lines_utilisateur as $user_line) {
                $utilisateurs_text .= "• " . $user_line->type . ": " . $user_line->descriptif . "\n";
            }
        }
        $substitutions['__SITE_UTILISATEURS__'] = $utilisateurs_text;
        
        // Informations entreprise (Dolibarr)
        $substitutions['__ENTREPRISE_NOM__'] = $conf->global->MAIN_INFO_SOCIETE_NOM ?: '';
        $substitutions['__ENTREPRISE_ADRESSE__'] = $conf->global->MAIN_INFO_SOCIETE_ADDRESS ?: '';
        $substitutions['__ENTREPRISE_CP__'] = $conf->global->MAIN_INFO_SOCIETE_ZIP ?: '';
        $substitutions['__ENTREPRISE_VILLE__'] = $conf->global->MAIN_INFO_SOCIETE_TOWN ?: '';
        $substitutions['__ENTREPRISE_TEL__'] = $conf->global->MAIN_INFO_SOCIETE_TEL ?: '';
        $substitutions['__ENTREPRISE_EMAIL__'] = $conf->global->MAIN_INFO_SOCIETE_MAIL ?: '';
        $substitutions['__ENTREPRISE_SIRET__'] = $conf->global->MAIN_INFO_SIRET ?: '';
        
        return $substitutions;
    }
    
    /**
     * Génère le document final
     */
    private function generateDocument($template_file, $substitutions, $site, $type)
    {
        global $conf;
        
        // Créer le répertoire de sortie
        $output_dir = $conf->rendezvousclient->multidir_output[$conf->entity] . '/sites/' . $site->rowid . '/';
        if (!is_dir($output_dir)) {
            dol_mkdir($output_dir);
        }
        
        // Nom du fichier de sortie
        $filename = $type . '_' . $site->nom . '_' . date('Y-m-d_H-i-s') . '.odt';
        $output_file = $output_dir . $filename;
        
        // Copier le template
        if (!copy($template_file, $output_file)) {
            $this->error = "Impossible de copier le template";
            return false;
        }
        
        // Traiter le fichier ODT
        if ($this->processOdtFile($output_file, $substitutions)) {
            return $output_file;
        }
        
        return false;
    }
    
    /**
     * Traite le fichier ODT pour faire les substitutions
     */
    private function processOdtFile($file_path, $substitutions)
    {
        // Un fichier ODT est un ZIP contenant des fichiers XML
        $zip = new ZipArchive();
        
        if ($zip->open($file_path) !== TRUE) {
            $this->error = "Impossible d'ouvrir le fichier ODT";
            return false;
        }
        
        // Lire le contenu principal (content.xml)
        $content = $zip->getFromName('content.xml');
        if ($content === false) {
            $this->error = "Impossible de lire content.xml";
            $zip->close();
            return false;
        }
        
        // Faire les substitutions
        foreach ($substitutions as $search => $replace) {
            $content = str_replace($search, htmlspecialchars($replace, ENT_XML1), $content);
        }
        
        // Réécrire le contenu
        if (!$zip->deleteName('content.xml') || !$zip->addFromString('content.xml', $content)) {
            $this->error = "Impossible de mettre à jour content.xml";
            $zip->close();
            return false;
        }
        
        $zip->close();
        return true;
    }
    
    /**
     * Génère un document pour un rendez-vous
     */
    public function generateRdvDocument($rdv, $type = 'compte_rendu')
    {
        // Similaire à generateSiteDocument mais pour les rendez-vous
        // À implémenter selon vos besoins
        return false;
    }
    
    /**
     * Liste les templates disponibles
     */
    public function getAvailableTemplates()
    {
        $templates_dir = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/';
        $templates = array();
        
        if (is_dir($templates_dir)) {
            $files = scandir($templates_dir);
            foreach ($files as $file) {
                if (pathinfo($file, PATHINFO_EXTENSION) == 'odt') {
                    $templates[] = $file;
                }
            }
        }
        
        return $templates;
    }
}
