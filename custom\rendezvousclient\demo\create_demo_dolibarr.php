<?php
/**
 *	\file       rendezvousclient/demo/create_demo_dolibarr.php
 *	\ingroup    rendezvousclient
 *	\brief      Création automatique d'un Dolibarr de démonstration
 */

require_once '../../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

dol_include_once('/rendezvousclient/site/class/site.class.php');

$action = GETPOST('action', 'aZ09');
$site_id = GETPOST('site_id', 'int');
$help_url = '';

/*
 * Actions
 */

if ($action == 'create_demo') {
    $site = new Site($db);
    $result = $site->fetch($site_id);
    
    if ($result > 0) {
        // Récupérer les modules sélectionnés pour ce site
        $modules_sql = "SELECT m.nom_module, m.description, m.version 
                       FROM ".MAIN_DB_PREFIX."avimm_constante_module m
                       INNER JOIN ".MAIN_DB_PREFIX."rendez_vous_site_module sm ON m.rowid = sm.fk_module
                       WHERE sm.fk_site = ".$site_id;
        
        $resql = $db->query($modules_sql);
        $selected_modules = array();
        
        if ($resql) {
            while ($obj = $db->fetch_object($resql)) {
                $selected_modules[] = array(
                    'nom' => $obj->nom_module,
                    'description' => $obj->description,
                    'version' => $obj->version
                );
            }
        }
        
        // Créer le répertoire de démonstration
        $demo_name = 'demo_' . $site->nom . '_' . date('Y-m-d_H-i-s');
        $demo_path = DOL_DOCUMENT_ROOT . '/../demos/' . $demo_name;
        
        if (!is_dir(dirname($demo_path))) {
            mkdir(dirname($demo_path), 0755, true);
        }
        
        // Copier les fichiers Dolibarr de base
        $source_path = DOL_DOCUMENT_ROOT;
        
        if (copyDolibarrFiles($source_path, $demo_path)) {
            // Créer la base de données de démonstration
            $demo_db_name = 'dolibarr_demo_' . preg_replace('/[^a-zA-Z0-9_]/', '_', $site->nom);
            
            if (createDemoDatabase($demo_db_name)) {
                // Configurer Dolibarr pour la démo
                if (configureDemoDolibarr($demo_path, $demo_db_name, $site, $selected_modules)) {
                    // Activer les modules sélectionnés
                    if (activateSelectedModules($demo_path, $demo_db_name, $selected_modules)) {
                        setEventMessages("Dolibarr de démonstration créé avec succès : $demo_name", null, 'mesgs');
                        
                        // Enregistrer les informations de la démo
                        $demo_url = 'http://' . $_SERVER['HTTP_HOST'] . '/demos/' . $demo_name;
                        saveDemoInfo($site_id, $demo_name, $demo_url, $demo_db_name);
                        
                    } else {
                        setEventMessages("Erreur lors de l'activation des modules", null, 'errors');
                    }
                } else {
                    setEventMessages("Erreur lors de la configuration de Dolibarr", null, 'errors');
                }
            } else {
                setEventMessages("Erreur lors de la création de la base de données", null, 'errors');
            }
        } else {
            setEventMessages("Erreur lors de la copie des fichiers Dolibarr", null, 'errors');
        }
    } else {
        setEventMessages("Site non trouvé", null, 'errors');
    }
}

/**
 * Copie les fichiers Dolibarr essentiels
 */
function copyDolibarrFiles($source, $destination) {
    if (!is_dir($destination)) {
        mkdir($destination, 0755, true);
    }
    
    // Fichiers et dossiers essentiels à copier
    $essential_files = array(
        'htdocs',
        'scripts'
    );
    
    foreach ($essential_files as $file) {
        $src = $source . '/' . $file;
        $dst = $destination . '/' . $file;
        
        if (is_dir($src)) {
            if (!copyDirectory($src, $dst)) {
                return false;
            }
        } elseif (is_file($src)) {
            if (!copy($src, $dst)) {
                return false;
            }
        }
    }
    
    return true;
}

/**
 * Copie récursive d'un répertoire
 */
function copyDirectory($src, $dst) {
    if (!is_dir($src)) return false;
    
    if (!is_dir($dst)) {
        mkdir($dst, 0755, true);
    }
    
    $files = scandir($src);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            $srcFile = $src . '/' . $file;
            $dstFile = $dst . '/' . $file;
            
            if (is_dir($srcFile)) {
                copyDirectory($srcFile, $dstFile);
            } else {
                copy($srcFile, $dstFile);
            }
        }
    }
    
    return true;
}

/**
 * Crée la base de données de démonstration
 */
function createDemoDatabase($db_name) {
    global $db;
    
    // Créer la base de données
    $sql = "CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8 COLLATE utf8_general_ci";
    $resql = $db->query($sql);
    
    if (!$resql) {
        return false;
    }
    
    // Copier la structure de la base actuelle
    $current_db = $db->database_name;
    
    // Exporter la structure
    $tables_sql = "SHOW TABLES FROM `$current_db`";
    $resql = $db->query($tables_sql);
    
    if ($resql) {
        while ($obj = $db->fetch_row($resql)) {
            $table = $obj[0];
            
            // Obtenir la structure de la table
            $create_sql = "SHOW CREATE TABLE `$current_db`.`$table`";
            $create_resql = $db->query($create_sql);
            
            if ($create_resql) {
                $create_obj = $db->fetch_row($create_resql);
                $create_statement = $create_obj[1];
                
                // Adapter pour la nouvelle base
                $create_statement = str_replace("CREATE TABLE `$table`", "CREATE TABLE `$db_name`.`$table`", $create_statement);
                
                $db->query($create_statement);
            }
        }
    }
    
    return true;
}

/**
 * Configure Dolibarr pour la démonstration
 */
function configureDemoDolibarr($demo_path, $db_name, $site, $modules) {
    // Créer le fichier conf.php
    $conf_content = '<?php
// Dolibarr configuration file for demo
$dolibarr_main_url_root = "http://' . $_SERVER['HTTP_HOST'] . '/demos/' . basename($demo_path) . '/htdocs";
$dolibarr_main_document_root = "' . $demo_path . '/htdocs";
$dolibarr_main_url_root_alt = "";
$dolibarr_main_document_root_alt = "";
$dolibarr_main_data_root = "' . $demo_path . '/documents";
$dolibarr_main_db_host = "localhost";
$dolibarr_main_db_port = "3306";
$dolibarr_main_db_name = "' . $db_name . '";
$dolibarr_main_db_prefix = "llx_";
$dolibarr_main_db_user = "' . $conf->db->user . '";
$dolibarr_main_db_pass = "' . $conf->db->pass . '";
$dolibarr_main_db_type = "mysqli";
$dolibarr_main_db_character_set = "utf8";
$dolibarr_main_db_collation = "utf8_unicode_ci";
$dolibarr_main_authentication = "dolibarr";
$dolibarr_main_demo = "1";
$dolibarr_main_prod = "0";
$dolibarr_main_force_https = "0";
$dolibarr_main_restrict_os_commands = "mysqldump, mysql, pg_dump, pgrestore";
$dolibarr_nocsrfcheck = "0";
$dolibarr_main_instance_unique_id = "' . uniqid() . '";
$dolibarr_mailing_limit_sendbyweb = "0";
$dolibarr_mailing_limit_sendbycli = "0";
';
    
    $conf_path = $demo_path . '/htdocs/conf/conf.php';
    
    if (!is_dir(dirname($conf_path))) {
        mkdir(dirname($conf_path), 0755, true);
    }
    
    return file_put_contents($conf_path, $conf_content) !== false;
}

/**
 * Active les modules sélectionnés
 */
function activateSelectedModules($demo_path, $db_name, $modules) {
    // Mapping des modules Dolibarr
    $dolibarr_modules = array(
        'Societe' => 'modSociete',
        'Facture' => 'modFacture',
        'Commande' => 'modCommande',
        'Projet' => 'modProjet',
        'Contrat' => 'modContrat',
        'Produit' => 'modProduct',
        'Stock' => 'modStock',
        'Expedition' => 'modExpedition',
        'Fournisseur' => 'modFournisseur',
        'Comptabilite' => 'modComptabilite',
        'Banque' => 'modBanque',
        'Adherent' => 'modAdherent',
        'Agenda' => 'modAgenda',
        'Resource' => 'modResource',
        'ECM' => 'modECM',
        'Website' => 'modWebsite'
    );
    
    // Créer un script d'activation
    $activation_script = '<?php
require_once "' . $demo_path . '/htdocs/main.inc.php";

// Activer les modules sélectionnés
';
    
    foreach ($modules as $module) {
        $module_name = $module['nom'];
        if (isset($dolibarr_modules[$module_name])) {
            $dolibarr_module = $dolibarr_modules[$module_name];
            $activation_script .= '
// Activer ' . $module_name . '
$res = activateModule("' . $dolibarr_module . '");
if ($res) {
    print "Module ' . $module_name . ' activé\n";
} else {
    print "Erreur activation ' . $module_name . '\n";
}
';
        }
    }
    
    $activation_script .= '
print "Activation des modules terminée\n";
?>';
    
    $script_path = $demo_path . '/activate_modules.php';
    file_put_contents($script_path, $activation_script);
    
    // Exécuter le script
    $output = shell_exec("php $script_path 2>&1");
    
    return true;
}

/**
 * Sauvegarde les informations de la démo
 */
function saveDemoInfo($site_id, $demo_name, $demo_url, $db_name) {
    global $db;
    
    $sql = "INSERT INTO ".MAIN_DB_PREFIX."rendez_vous_site_demo 
            (fk_site, nom_demo, url_demo, db_name, date_creation, statut)
            VALUES ($site_id, '".$db->escape($demo_name)."', '".$db->escape($demo_url)."', 
                   '".$db->escape($db_name)."', NOW(), 1)";
    
    return $db->query($sql);
}

/*
 * View
 */

$title = 'Création Dolibarr de démonstration';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script crée automatiquement une instance Dolibarr de démonstration avec les modules sélectionnés dans les paramètres du site.';
print '</div>';

// Sélection du site
if (!$site_id) {
    print '<h3>Sélectionner un site</h3>';
    
    $sql = "SELECT s.rowid, s.nom, soc.nom as nom_societe, s.version
            FROM ".MAIN_DB_PREFIX."rendez_vous_site s
            LEFT JOIN ".MAIN_DB_PREFIX."societe soc ON s.fk_societe = soc.rowid
            ORDER BY s.nom";
    
    $resql = $db->query($sql);
    
    if ($resql) {
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Site</th>';
        print '<th>Société</th>';
        print '<th>Version</th>';
        print '<th>Action</th>';
        print '</tr>';
        
        while ($obj = $db->fetch_object($resql)) {
            print '<tr class="oddeven">';
            print '<td><strong>'.$obj->nom.'</strong></td>';
            print '<td>'.$obj->nom_societe.'</td>';
            print '<td>'.$obj->version.'</td>';
            print '<td>';
            print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?site_id='.$obj->rowid.'">Sélectionner</a>';
            print '</td>';
            print '</tr>';
        }
        
        print '</table>';
    }
    
} else {
    // Afficher les détails du site sélectionné
    $site = new Site($db);
    $site->fetch($site_id);
    
    print '<h3>Site sélectionné : '.$site->nom.'</h3>';
    
    // Afficher les modules sélectionnés
    $modules_sql = "SELECT m.nom_module, m.description, m.version 
                   FROM ".MAIN_DB_PREFIX."avimm_constante_module m
                   INNER JOIN ".MAIN_DB_PREFIX."rendez_vous_site_module sm ON m.rowid = sm.fk_module
                   WHERE sm.fk_site = ".$site_id;
    
    $resql = $db->query($modules_sql);
    
    if ($resql && $db->num_rows($resql) > 0) {
        print '<h4>Modules qui seront activés :</h4>';
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Module</th>';
        print '<th>Description</th>';
        print '<th>Version</th>';
        print '</tr>';
        
        while ($obj = $db->fetch_object($resql)) {
            print '<tr class="oddeven">';
            print '<td><strong>'.$obj->nom_module.'</strong></td>';
            print '<td>'.$obj->description.'</td>';
            print '<td>'.$obj->version.'</td>';
            print '</tr>';
        }
        
        print '</table>';
        
        print '<br><div class="center">';
        print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=create_demo&site_id='.$site_id.'&token='.newToken().'">Créer le Dolibarr de démonstration</a>';
        print '</div>';
        
    } else {
        print '<div class="warning">';
        print 'Aucun module sélectionné pour ce site. Veuillez d\'abord configurer les modules dans les paramètres du site.';
        print '</div>';
    }
    
    print '<br><div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
    print '</div>';
}

// Afficher les démonstrations existantes
print '<br><h3>Démonstrations existantes</h3>';

$demos_sql = "SELECT d.*, s.nom as nom_site 
              FROM ".MAIN_DB_PREFIX."rendez_vous_site_demo d
              LEFT JOIN ".MAIN_DB_PREFIX."rendez_vous_site s ON d.fk_site = s.rowid
              ORDER BY d.date_creation DESC";

$resql = $db->query($demos_sql);

if ($resql && $db->num_rows($resql) > 0) {
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Site</th>';
    print '<th>Nom démo</th>';
    print '<th>Date création</th>';
    print '<th>Statut</th>';
    print '<th>Actions</th>';
    print '</tr>';
    
    while ($obj = $db->fetch_object($resql)) {
        print '<tr class="oddeven">';
        print '<td>'.$obj->nom_site.'</td>';
        print '<td><strong>'.$obj->nom_demo.'</strong></td>';
        print '<td>'.dol_print_date($db->jdate($obj->date_creation), 'dayhour').'</td>';
        print '<td>';
        if ($obj->statut == 1) {
            print '<span class="badge badge-status4 badge-status">Actif</span>';
        } else {
            print '<span class="badge badge-status8 badge-status">Inactif</span>';
        }
        print '</td>';
        print '<td>';
        if ($obj->statut == 1) {
            print '<a class="butAction" href="'.$obj->url_demo.'" target="_blank">Ouvrir</a>';
        }
        print '</td>';
        print '</tr>';
    }
    
    print '</table>';
} else {
    print '<div class="opacitymedium">Aucune démonstration créée</div>';
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créer un site</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
