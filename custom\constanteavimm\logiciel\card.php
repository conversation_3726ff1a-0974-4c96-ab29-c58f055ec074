<?php
/* Copyright (C) 2001-2005 <PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2004-2015 <PERSON>  <<EMAIL>>
 * Copyright (C) 2005-2012 <PERSON>        <<EMAIL>>
 * Copyright (C) 2015      <PERSON><PERSON><PERSON>	<<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/*
 *	\file       constanteavimm/logiciel/card.php
 *	\ingroup    constanteavimm
 *	\brief      fiche logiciel
*/

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) {
	$res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
}
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) {
	$i--; $j--;
}
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) {
	$res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
}
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) {
	$res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
}
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) {
	$res = @include "../main.inc.php";
}
if (!$res && file_exists("../../main.inc.php")) {
	$res = @include "../../main.inc.php";
}
if (!$res && file_exists("../../../main.inc.php")) {
	$res = @include "../../../main.inc.php";
}
if (!$res) {
	die("Include of main fails");
}

require_once DOL_DOCUMENT_ROOT.'/custom/constanteavimm/logiciel/class/logiciel.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/constanteavimm/lib/logiciel.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/doleditor.class.php';

// Load translation files required by the page
$langs->loadLangs(array('constanteavimm'));

$action = GETPOST('action');
$id = GETPOST('id');

$object = new Logiciel($db);
if ($id > 0) {
	$object->fetch($id);
}

/*
 * Actions
*/ 

if($action == 'add' && GETPOST('save')){
    $error = 0;

    if(!GETPOST('libelle')){
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentities('libelle')), null, 'errors');
        $error++;
    }
    
    if(!$error){
        $object->libelle = GETPOST('libelle');
        $object->tarif = GETPOST('tarif');
        $object->cahier_des_charges = GETPOST('cahier_des_charges', 'restricthtml');
        
        $result = $object->create();

        if ($result >= 0 && !$error) {
            $url = DOL_URL_ROOT."/custom/constanteavimm/logiciel/card.php?id=".$object->id;

            header("Location: ".$url);
            exit;
        } else {
            $langs->load("errors");
            setEventMessages($object->error, $object->errors, 'errors');
            $db->rollback();
            $action = 'create';
        }
    }else{
        $action = 'create';
    }
}

if($action == 'update'){
    $error = 0;

    if(!GETPOST('libelle')){
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentities('libelle')), null, 'errors');
        $error++;
    }
    
    if(!$error){
        $object->libelle = GETPOST('libelle');
        $object->tarif = GETPOST('tarif');
        $object->cahier_des_charges = GETPOST('cahier_des_charges', 'restricthtml');
        
        $result = $object->update($object->id);

        if ($result > 0 && !$error) {
            $url = DOL_URL_ROOT."/custom/constanteavimm/logiciel/card.php?id=".$object->id;

            header("Location: ".$url);
            exit;
        } else {
            $langs->load("errors");
            setEventMessages($object->error, $object->errors, 'errors');
            $action = 'edit';
        }
    }else{
        $action = 'edit';
    }
}

if($action == 'confirm_delete' && GETPOST('confirm') == 'yes'){

    $res = $object->delete($object->id);

    if($res){
        setEventMessages($langs->trans("LogicielDeleted", $object->libelle), null, 'mesgs');
        header("Location: ".DOL_URL_ROOT."/custom/constanteavimm/logiciel/list.php?restore_lastsearch_values=1");
        exit;
    }else{
        $langs->load("errors");
        setEventMessages($object->error, $object->errors, 'errors');
        $action = '';
    }
}

/*
 * View
*/

$form = new Form($db);

$title = $langs->trans('logiciels')." - ".$langs->trans('Card');

llxHeader('', $title, $help_url);

if($action == 'create'){
    /*
     * Mode création
    */

    print load_fiche_titre($langs->trans('newlogiciel'), '', 'tools');

    print '<form enctype="multipart/form-data" action="'.$_SERVER["PHP_SELF"].'" method="post" name="formlogiciel" autocomplete="off">';
    print '<input type="hidden" name="action" value="add">';
    print '<input type="hidden" name="token" value="'.newToken().'">';

    print dol_get_fiche_head(null, 'card', '', 0, '');

    print '<table class="border centpercent">';

    // Libelle
    print '<tr><td class="titlefieldcreate fieldrequired">'.$form->editfieldkey('libelle', 'libelle', '', $object, 0).'</td>';
    print '<td>';
    print '<input type="text" class="minwidth300" maxlength="100" name="libelle" id="libelle" value="'.(GETPOST('libelle') ? GETPOST('libelle') : $object->libelle).'">';
    print '</td>';
    print '</tr>';

    // Tarif
    print '<tr>';
    print '<td class="titlefieldcreate">Tarif</td>';
    print '<td>';
    print '<input type="text" style="min-width: 600px" maxlength="255" name="tarif" id="tarif" value="'.(GETPOST('tarif') ? GETPOST('tarif') : $object->tarif).'">';
    print '</td>';
    print '</tr>';

    // Cahier des charges
    print '<tr>';
	print '<td class="titlefieldcreate">Cahier des charges</td>';
	print '<td>';
	$doleditor = new DolEditor('cahier_des_charges', (GETPOST('cahier_des_charges') ? GETPOST('cahier_des_charges') : $object->cahier_des_charges), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '</table>';

    print dol_get_fiche_end();

    print $form->buttonsSaveCancel("AddLogiciel", 'Cancel', null, 0, '', 0);

    print '</form>';


}elseif ($action == 'edit') {
    /*
     * Mode édition
    */

    $head = logiciel_prepare_head($object);

    print dol_get_fiche_head($head, 'card', $langs->trans("logiciel"), -1, 'tools');

    print '<form enctype="multipart/form-data" action="'.$_SERVER["PHP_SELF"].'" method="post" name="formlogiciel" autocomplete="off">';
    print '<input type="hidden" name="action" value="update">';
    print '<input type="hidden" name="id" value="'.$object->id.'">';
    print '<input type="hidden" name="token" value="'.newToken().'">';

    print '<table class="border centpercent">';

    // Libelle
    print '<tr><td class="titlefieldupdate titlefieldcreate fieldrequired">'.$form->editfieldkey('libelle', 'libelle', '', $object, 0).'</td>';
    print '<td>';
    print '<input type="text" class="minwidth300" maxlength="100" name="libelle" id="libelle" value="'.(GETPOST('libelle') ? GETPOST('libelle') : $object->libelle).'">';
    print '</td>';
    print '</tr>';

    // Tarif
    print '<tr>';
    print '<td class="titlefieldupdate">Tarif</td>';
    print '<td>';
    print '<input type="text" style="min-width: 600px" maxlength="255" name="tarif" id="tarif" value="'.(GETPOST('tarif') ? GETPOST('tarif') : $object->tarif).'">';
    print '</td>';
    print '</tr>';

    // Cahier des charges
    print '<tr>';
	print '<td class="titlefieldupdate">Cahier des charges</td>';
	print '<td>';
	$doleditor = new DolEditor('cahier_des_charges', $object->cahier_des_charges, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '</table>';

    print dol_get_fiche_end();

    print $form->buttonsSaveCancel("Save", 'Cancel', null, 0, '', 0);

    print '</form>';

}else{
    /*
     * Mode visualisation
    */ 

    $head = logiciel_prepare_head($object);

    print dol_get_fiche_head($head, 'card', $langs->trans("logiciel"), -1, 'tools');
    
    if($action == 'delete'){
        $formconfirm = $form->formconfirm($_SERVER["PHP_SELF"]."?id=".$object->id, $langs->trans('DeleteALogiciel'), $langs->trans('ConfirmDeleteLogiciel'), "confirm_delete", '', 1, 1);
    }
    // var_dump($formconfirm);
    // Print form confirm
    print $formconfirm;

    $morehtmlref = '<div class="refidno">';
    $morehtmlref .= $object->libelle;
    $morehtmlref .= '</div>';

    print '
    <div class="arearef heightref valignmiddle centpercent">
        <!-- Start banner content -->
        <div style="vertical-align: middle">
            <div class="pagination paginationref">
                <ul class="right">
                    <li class="noborder litext clearbothonsmartphone">
                        <a href="'.DOL_URL_ROOT.'/custom/constanteavimm/logiciel/list.php?restore_lastsearch_values=1">'.$langs->trans("BackToList").'</a>
                    </li>
                </ul>
            </div>
            <!-- morehtmlleft -->
            <div class="inline-block floatleft">
                <div class="floatleft inline-block valignmiddle divphotoref">
                    <div class="divforspanimg photowithmargin photoref">
                        '.img_picto('', 'tools').'
                    </div>
                    <div class="difforspanimgright"></div>
                </div>
            </div>
            <div class="inline-block floatleft valignmiddle maxwidth750 marginbottomonly refid refidpadding">
                '.$object->libelle.'
            </div>
        </div>
        <!-- End banner content -->
    </div>';

    print '<div class="fichecenter">';
    print '<div class="fichehalfleft">';

    print '<div class="underbanner clearboth"></div>';
    print '<table class="border tableforfield centpercent">';
    
    // Libelle
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans('libelle').'</td>';
    print '<td>'.$object->libelle.'</td>';
    print '</tr>';

    // Tarif
    print '<tr>';
    print '<td class="titlefield">Tarif</td>';
    print '<td>'.price($object->tarif).'</td>';
    print '</tr>';

    // Cahier des charges
    print '<tr>';
    print '<td class="titlefield">Cahier des charges</td>';
    print '<td>'.dol_htmlentitiesbr($object->cahier_des_charges).'</td>';
    print '</tr>';

    print '</table>';
    print '</div>';
    
    print '<div class="fichehalfleft">';
    print '</div>';

    print '</div>';

    print '<div style="clear:both"></div>';

    print dol_get_fiche_end();

    /*
    *  Actions
    */
    print '<div class="tabsAction">'."\n";

    print dolGetButtonAction('', $langs->trans('Modify'), 'default', $_SERVER["PHP_SELF"].'?id='.$object->id.'&action=edit&token='.newToken(), '', 1);

    print dolGetButtonAction($langs->trans('Delete'), '', 'delete', $_SERVER["PHP_SELF"].'?id='.$object->id.'&action=delete&token='.newToken(), 'action-delete-no-ajax', 1);

    print '</div>';

}

// End of page
llxFooter();
$db->close();