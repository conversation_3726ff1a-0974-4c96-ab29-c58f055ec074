<?php
/**
 *	\file       rendezvousclient/demo/simple_demo_creator.php
 *	\ingroup    rendezvousclient
 *	\brief      Créateur simple de démonstration Dolibarr
 */

require_once '../../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

dol_include_once('/rendezvousclient/site/class/site.class.php');

$action = GETPOST('action', 'aZ09');
$site_id = GETPOST('site_id', 'int');
$help_url = '';

/*
 * Actions
 */

if ($action == 'create_simple_demo') {
    $site = new Site($db);
    $result = $site->fetch($site_id);
    
    if ($result > 0) {
        // Récupérer les modules sélectionnés pour ce site
        $modules_sql = "SELECT m.nom_module, m.description 
                       FROM ".MAIN_DB_PREFIX."avimm_constante_module m
                       INNER JOIN ".MAIN_DB_PREFIX."rendez_vous_site_module sm ON m.rowid = sm.fk_module
                       WHERE sm.fk_site = ".$site_id;
        
        $resql = $db->query($modules_sql);
        $selected_modules = array();
        
        if ($resql) {
            while ($obj = $db->fetch_object($resql)) {
                $selected_modules[] = $obj->nom_module;
            }
        }
        
        // Créer un nom unique pour la démo
        $demo_name = 'demo_' . preg_replace('/[^a-zA-Z0-9_]/', '_', $site->nom) . '_' . date('Ymd_His');
        $demo_url = 'http://' . $_SERVER['HTTP_HOST'] . '/dolibarr-demo/' . $demo_name;
        
        // Créer un script de configuration pour la démo
        $config_script = createDemoConfigScript($site, $selected_modules, $demo_name);
        
        // Enregistrer les informations de la démo
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."rendez_vous_site_demo 
                (fk_site, nom_demo, url_demo, db_name, chemin_demo, date_creation, fk_user_creation, statut)
                VALUES ($site_id, '".$db->escape($demo_name)."', '".$db->escape($demo_url)."', 
                       '".$db->escape('demo_'.$demo_name)."', '".$db->escape('/dolibarr-demo/'.$demo_name)."',
                       NOW(), ".$user->id.", 1)";
        
        $resql = $db->query($sql);
        
        if ($resql) {
            setEventMessages("Démonstration créée avec succès : $demo_name", null, 'mesgs');
            
            // Afficher les instructions
            $_SESSION['demo_instructions'] = array(
                'name' => $demo_name,
                'url' => $demo_url,
                'modules' => $selected_modules,
                'config_script' => $config_script
            );
            
        } else {
            setEventMessages("Erreur lors de l'enregistrement de la démonstration", null, 'errors');
        }
    } else {
        setEventMessages("Site non trouvé", null, 'errors');
    }
}

/**
 * Crée un script de configuration pour la démo
 */
function createDemoConfigScript($site, $modules, $demo_name) {
    $script = "#!/bin/bash\n";
    $script .= "# Script de création de démonstration Dolibarr\n";
    $script .= "# Site: " . $site->nom . "\n";
    $script .= "# Date: " . date('Y-m-d H:i:s') . "\n\n";
    
    $script .= "# Variables\n";
    $script .= "DEMO_NAME=\"$demo_name\"\n";
    $script .= "DEMO_PATH=\"/var/www/html/dolibarr-demo/\$DEMO_NAME\"\n";
    $script .= "DB_NAME=\"demo_\$DEMO_NAME\"\n";
    $script .= "DOLIBARR_SOURCE=\"/var/www/html/dolibarr\"\n\n";
    
    $script .= "# Créer le répertoire de démonstration\n";
    $script .= "mkdir -p \$DEMO_PATH\n";
    $script .= "cp -r \$DOLIBARR_SOURCE/* \$DEMO_PATH/\n\n";
    
    $script .= "# Créer la base de données\n";
    $script .= "mysql -u root -p -e \"CREATE DATABASE \$DB_NAME CHARACTER SET utf8 COLLATE utf8_general_ci;\"\n";
    $script .= "mysql -u root -p \$DB_NAME < /var/www/html/dolibarr/install/mysql/tables/llx_*.sql\n\n";
    
    $script .= "# Configuration Dolibarr\n";
    $script .= "cat > \$DEMO_PATH/htdocs/conf/conf.php << 'EOF'\n";
    $script .= "<?php\n";
    $script .= "\$dolibarr_main_url_root = 'http://" . $_SERVER['HTTP_HOST'] . "/dolibarr-demo/\$DEMO_NAME/htdocs';\n";
    $script .= "\$dolibarr_main_document_root = '\$DEMO_PATH/htdocs';\n";
    $script .= "\$dolibarr_main_data_root = '\$DEMO_PATH/documents';\n";
    $script .= "\$dolibarr_main_db_host = 'localhost';\n";
    $script .= "\$dolibarr_main_db_name = '\$DB_NAME';\n";
    $script .= "\$dolibarr_main_db_user = 'root';\n";
    $script .= "\$dolibarr_main_db_pass = 'votre_mot_de_passe';\n";
    $script .= "\$dolibarr_main_db_type = 'mysqli';\n";
    $script .= "\$dolibarr_main_demo = '1';\n";
    $script .= "EOF\n\n";
    
    $script .= "# Modules à activer\n";
    foreach ($modules as $module) {
        $script .= "# Module: $module\n";
    }
    
    $script .= "\necho \"Démonstration créée avec succès !\"\n";
    $script .= "echo \"URL: http://" . $_SERVER['HTTP_HOST'] . "/dolibarr-demo/\$DEMO_NAME/htdocs\"\n";
    
    return $script;
}

/*
 * View
 */

$title = 'Créateur simple de démonstration';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script génère les instructions pour créer une démonstration Dolibarr avec les modules sélectionnés.';
print '</div>';

// Afficher les instructions si une démo vient d'être créée
if (!empty($_SESSION['demo_instructions'])) {
    $demo = $_SESSION['demo_instructions'];
    
    print '<div class="ok">';
    print '<h3>Démonstration créée : ' . $demo['name'] . '</h3>';
    print '<p><strong>URL prévue :</strong> ' . $demo['url'] . '</p>';
    print '<p><strong>Modules à activer :</strong> ' . implode(', ', $demo['modules']) . '</p>';
    print '</div>';
    
    print '<h3>Script de configuration</h3>';
    print '<div class="info">Copiez et exécutez ce script sur votre serveur :</div>';
    
    print '<textarea style="width: 100%; height: 300px; font-family: monospace; font-size: 12px;">';
    print htmlentities($demo['config_script']);
    print '</textarea>';
    
    print '<br><br>';
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'">Créer une nouvelle démonstration</a>';
    print '</div>';
    
    // Nettoyer la session
    unset($_SESSION['demo_instructions']);
    
} else {
    // Sélection du site
    if (!$site_id) {
        print '<h3>Sélectionner un site</h3>';
        
        $sql = "SELECT s.rowid, s.nom, soc.nom as nom_societe, s.version
                FROM ".MAIN_DB_PREFIX."rendez_vous_site s
                LEFT JOIN ".MAIN_DB_PREFIX."societe soc ON s.fk_societe = soc.rowid
                ORDER BY s.nom";
        
        $resql = $db->query($sql);
        
        if ($resql && $db->num_rows($resql) > 0) {
            print '<table class="border centpercent">';
            print '<tr class="liste_titre">';
            print '<th>Site</th>';
            print '<th>Société</th>';
            print '<th>Version</th>';
            print '<th>Modules configurés</th>';
            print '<th>Action</th>';
            print '</tr>';
            
            while ($obj = $db->fetch_object($resql)) {
                // Compter les modules configurés
                $modules_sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."rendez_vous_site_module WHERE fk_site = ".$obj->rowid;
                $modules_resql = $db->query($modules_sql);
                $nb_modules = 0;
                if ($modules_resql) {
                    $modules_obj = $db->fetch_object($modules_resql);
                    $nb_modules = $modules_obj->nb;
                }
                
                print '<tr class="oddeven">';
                print '<td><strong>'.$obj->nom.'</strong></td>';
                print '<td>'.$obj->nom_societe.'</td>';
                print '<td>'.$obj->version.'</td>';
                print '<td>'.$nb_modules.' module(s)</td>';
                print '<td>';
                if ($nb_modules > 0) {
                    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?site_id='.$obj->rowid.'">Sélectionner</a>';
                } else {
                    print '<span class="opacitymedium">Aucun module</span>';
                }
                print '</td>';
                print '</tr>';
            }
            
            print '</table>';
        } else {
            print '<div class="warning">Aucun site configuré. Veuillez d\'abord créer un site.</div>';
        }
        
    } else {
        // Afficher les détails du site sélectionné
        $site = new Site($db);
        $site->fetch($site_id);
        
        print '<h3>Site sélectionné : '.$site->nom.'</h3>';
        
        // Afficher les modules sélectionnés
        $modules_sql = "SELECT m.nom_module, m.description 
                       FROM ".MAIN_DB_PREFIX."avimm_constante_module m
                       INNER JOIN ".MAIN_DB_PREFIX."rendez_vous_site_module sm ON m.rowid = sm.fk_module
                       WHERE sm.fk_site = ".$site_id;
        
        $resql = $db->query($modules_sql);
        
        if ($resql && $db->num_rows($resql) > 0) {
            print '<h4>Modules qui seront configurés :</h4>';
            print '<table class="border centpercent">';
            print '<tr class="liste_titre">';
            print '<th>Module</th>';
            print '<th>Description</th>';
            print '</tr>';
            
            while ($obj = $db->fetch_object($resql)) {
                print '<tr class="oddeven">';
                print '<td><strong>'.$obj->nom_module.'</strong></td>';
                print '<td>'.$obj->description.'</td>';
                print '</tr>';
            }
            
            print '</table>';
            
            print '<br><div class="center">';
            print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=create_simple_demo&site_id='.$site_id.'&token='.newToken().'">Générer le script de démonstration</a>';
            print '</div>';
            
        } else {
            print '<div class="warning">';
            print 'Aucun module sélectionné pour ce site. Veuillez d\'abord configurer les modules dans les paramètres du site.';
            print '</div>';
        }
        
        print '<br><div class="center">';
        print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
        print '</div>';
    }
}

// Afficher les démonstrations existantes
print '<br><h3>Démonstrations existantes</h3>';

$demos_sql = "SELECT d.*, s.nom as nom_site 
              FROM ".MAIN_DB_PREFIX."rendez_vous_site_demo d
              LEFT JOIN ".MAIN_DB_PREFIX."rendez_vous_site s ON d.fk_site = s.rowid
              ORDER BY d.date_creation DESC";

$resql = $db->query($demos_sql);

if ($resql && $db->num_rows($resql) > 0) {
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Site</th>';
    print '<th>Nom démo</th>';
    print '<th>Date création</th>';
    print '<th>URL prévue</th>';
    print '<th>Statut</th>';
    print '</tr>';
    
    while ($obj = $db->fetch_object($resql)) {
        print '<tr class="oddeven">';
        print '<td>'.$obj->nom_site.'</td>';
        print '<td><strong>'.$obj->nom_demo.'</strong></td>';
        print '<td>'.dol_print_date($db->jdate($obj->date_creation), 'dayhour').'</td>';
        print '<td><a href="'.$obj->url_demo.'" target="_blank">'.$obj->url_demo.'</a></td>';
        print '<td>';
        if ($obj->statut == 1) {
            print '<span class="badge badge-status4 badge-status">Configuré</span>';
        } else {
            print '<span class="badge badge-status8 badge-status">Inactif</span>';
        }
        print '</td>';
        print '</tr>';
    }
    
    print '</table>';
} else {
    print '<div class="opacitymedium">Aucune démonstration créée</div>';
}

// Instructions d'utilisation
print '<br><h3>Instructions d\'utilisation</h3>';
print '<div class="info">';
print '<ol>';
print '<li><strong>Sélectionnez un site</strong> avec des modules configurés</li>';
print '<li><strong>Générez le script</strong> de configuration automatique</li>';
print '<li><strong>Copiez le script</strong> et exécutez-le sur votre serveur</li>';
print '<li><strong>Accédez à la démonstration</strong> via l\'URL générée</li>';
print '</ol>';
print '</div>';

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créer un site</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/install_missing_tables.php">Installer les tables</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
