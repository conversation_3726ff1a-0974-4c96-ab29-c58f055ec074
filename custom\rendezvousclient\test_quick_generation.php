<?php
/**
 *	\file       rendezvousclient/test_quick_generation.php
 *	\ingroup    rendezvousclient
 *	\brief      Test rapide de génération de documents ODT
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

dol_include_once('/rendezvousclient/core/class/document_generator.class.php');
dol_include_once('/rendezvousclient/site/class/site.class.php');

$action = GETPOST('action', 'aZ09');
$site_id = GETPOST('site_id', 'int');

/*
 * Actions
 */

if ($action == 'test_quick' && $site_id > 0) {
    try {
        // Charger le site
        $site = new Site($db);
        $result = $site->fetch($site_id);
        
        if ($result <= 0) {
            throw new Exception("Site non trouvé (ID: $site_id)");
        }
        
        // Charger les données associées
        $societe = new Societe($db);
        $projet = new Project($db);
        
        if ($site->fk_projet > 0) {
            $projet->fetch($site->fk_projet);
            if ($projet->socid > 0) {
                $societe->fetch($projet->socid);
            }
        }
        
        // Créer le générateur
        $generator = new DocumentGenerator($db);
        
        // Préparer les substitutions
        $substitutions = $generator->prepareSubstitutions($site, $societe, $projet);
        
        // Afficher les substitutions pour debug
        print '<h3>Variables générées :</h3>';
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Variable</th>';
        print '<th>Valeur</th>';
        print '</tr>';
        
        foreach ($substitutions as $var => $value) {
            print '<tr class="oddeven">';
            print '<td><code>'.$var.'</code></td>';
            print '<td>'.htmlspecialchars($value).'</td>';
            print '</tr>';
        }
        
        print '</table>';
        
        // Test de création d'un fichier ODT simple
        $templates_dir = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/';
        if (!is_dir($templates_dir)) {
            dol_mkdir($templates_dir);
        }
        
        $test_template = $templates_dir . 'test_quick.odt';
        
        // Créer un template simple si il n'existe pas
        if (!file_exists($test_template)) {
            $content_xml = '<?xml version="1.0" encoding="UTF-8"?>
<office:document-content xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0">
<office:body>
<office:text>
<text:h text:style-name="Heading_20_1" text:outline-level="1">Test rapide de génération</text:h>
<text:p text:style-name="Standard"/>
<text:p text:style-name="Standard">Date : __DATE_GENERATION__</text:p>
<text:p text:style-name="Standard">Utilisateur : __UTILISATEUR__</text:p>
<text:p text:style-name="Standard"/>
<text:p text:style-name="Standard">Client : __SOCIETE_NOM__</text:p>
<text:p text:style-name="Standard">Site : __SITE_NOM__</text:p>
<text:p text:style-name="Standard">Type : __SITE_TYPE__</text:p>
<text:p text:style-name="Standard"/>
<text:p text:style-name="Standard">Utilisateurs :</text:p>
<text:p text:style-name="Standard">__SITE_UTILISATEURS__</text:p>
</office:text>
</office:body>
</office:document-content>';
            
            $zip = new ZipArchive();
            if ($zip->open($test_template, ZipArchive::CREATE) === TRUE) {
                $zip->addFromString('mimetype', 'application/vnd.oasis.opendocument.text');
                $zip->addFromString('content.xml', $content_xml);
                
                $manifest = '<?xml version="1.0" encoding="UTF-8"?>
<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0">
<manifest:file-entry manifest:full-path="/" manifest:media-type="application/vnd.oasis.opendocument.text"/>
<manifest:file-entry manifest:full-path="content.xml" manifest:media-type="text/xml"/>
</manifest:manifest>';
                
                $zip->addFromString('META-INF/manifest.xml', $manifest);
                $zip->close();
                
                setEventMessages("Template de test créé", null, 'mesgs');
            }
        }
        
        // Générer le document
        if (file_exists($test_template)) {
            $output_dir = DOL_DATA_ROOT . '/rendezvousclient/sites/' . $site->rowid . '/';
            if (!is_dir($output_dir)) {
                dol_mkdir($output_dir);
            }
            
            $filename = 'test_quick_' . date('Y-m-d_H-i-s') . '.odt';
            $output_file = $output_dir . $filename;
            
            // Copier le template
            if (copy($test_template, $output_file)) {
                // Traiter le fichier ODT
                $zip = new ZipArchive();
                
                if ($zip->open($output_file) === TRUE) {
                    $content = $zip->getFromName('content.xml');
                    
                    if ($content !== false) {
                        // Faire les substitutions
                        foreach ($substitutions as $search => $replace) {
                            $content = str_replace($search, htmlspecialchars($replace, ENT_XML1), $content);
                        }
                        
                        // Réécrire le contenu
                        if ($zip->deleteName('content.xml') && $zip->addFromString('content.xml', $content)) {
                            $zip->close();
                            
                            setEventMessages("Document généré avec succès : $filename", null, 'mesgs');
                            
                            print '<br><div class="center">';
                            print '<a href="'.DOL_URL_ROOT.'/document.php?modulepart=rendezvousclient&file=sites/'.$site->rowid.'/'.$filename.'" target="_blank" class="butAction">Télécharger le document</a>';
                            print '</div>';
                        } else {
                            throw new Exception("Impossible de mettre à jour le contenu XML");
                        }
                    } else {
                        throw new Exception("Impossible de lire le contenu XML");
                    }
                } else {
                    throw new Exception("Impossible d'ouvrir le fichier ODT généré");
                }
            } else {
                throw new Exception("Impossible de copier le template");
            }
        } else {
            throw new Exception("Template de test non trouvé");
        }
        
    } catch (Exception $e) {
        setEventMessages("Erreur : " . $e->getMessage(), null, 'errors');
    }
}

/*
 * View
 */

$title = 'Test rapide de génération ODT';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce test vérifie rapidement que la génération de documents fonctionne.';
print '</div>';

// Vérifications rapides
print '<h3>Vérifications</h3>';

$checks = array();

// ZipArchive
$checks[] = array(
    'test' => 'Extension ZipArchive',
    'result' => class_exists('ZipArchive'),
    'message' => class_exists('ZipArchive') ? 'Disponible' : 'Manquante'
);

// Classe DocumentGenerator
try {
    $generator = new DocumentGenerator($db);
    $checks[] = array(
        'test' => 'Classe DocumentGenerator',
        'result' => true,
        'message' => 'Chargée avec succès'
    );
} catch (Exception $e) {
    $checks[] = array(
        'test' => 'Classe DocumentGenerator',
        'result' => false,
        'message' => 'Erreur : ' . $e->getMessage()
    );
}

// Répertoire de sortie
$output_dir = DOL_DATA_ROOT . '/rendezvousclient/';
$checks[] = array(
    'test' => 'Répertoire de sortie',
    'result' => is_dir($output_dir) || dol_mkdir($output_dir),
    'message' => is_dir($output_dir) ? 'Existe' : 'Créé'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Vérification</th>';
print '<th>Résultat</th>';
print '<th>Message</th>';
print '</tr>';

foreach ($checks as $check) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$check['test'].'</strong></td>';
    print '<td>';
    if ($check['result']) {
        print '<span class="badge badge-status4 badge-status">OK</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">ERREUR</span>';
    }
    print '</td>';
    print '<td>'.$check['message'].'</td>';
    print '</tr>';
}

print '</table>';

// Sélection du site
if (!$site_id) {
    print '<br><h3>Sélectionner un site pour tester</h3>';
    
    $sql = "SELECT s.rowid, s.nom, soc.nom as nom_societe 
            FROM ".MAIN_DB_PREFIX."rendez_vous_site s
            LEFT JOIN ".MAIN_DB_PREFIX."projet p ON s.fk_projet = p.rowid
            LEFT JOIN ".MAIN_DB_PREFIX."societe soc ON p.socid = soc.rowid
            ORDER BY s.rowid DESC LIMIT 5";
    
    $resql = $db->query($sql);
    
    if ($resql && $db->num_rows($resql) > 0) {
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>ID</th>';
        print '<th>Site</th>';
        print '<th>Société</th>';
        print '<th>Action</th>';
        print '</tr>';
        
        while ($obj = $db->fetch_object($resql)) {
            print '<tr class="oddeven">';
            print '<td>'.$obj->rowid.'</td>';
            print '<td><strong>'.$obj->nom.'</strong></td>';
            print '<td>'.$obj->nom_societe.'</td>';
            print '<td>';
            print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_quick&site_id='.$obj->rowid.'&token='.newToken().'">Tester</a>';
            print '</td>';
            print '</tr>';
        }
        
        print '</table>';
    } else {
        print '<div class="warning">';
        print 'Aucun site trouvé. <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créez un site</a> pour tester.';
        print '</div>';
    }
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/diagnostic_generation.php">Diagnostic complet</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/admin/templates.php">Gestion templates</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créer un site</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
