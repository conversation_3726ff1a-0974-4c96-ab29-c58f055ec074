<?php
/*
 *  \file       rendezvousclient/site/class/democreator.class.php
 *  \ingroup    rendezvousclient
 *  \brief      Fichier de classe pour la création de démos Dolibarr
*/

require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
dol_include_once('/rendezvousclient/core/class/dolibarr_manager.class.php');

Class DemoCreator
{
    public $db;
    public $error;

    /**
     *	Constructor
     *
     *  @param		DoliDB		$db      Database handler
     */
    public function __construct($db)
    {
        $this->db = $db;
    }

    /**
     * Create a demo Dolibarr instance with selected modules
     *
     * @param Site $site Site object
     * @param array $modules_selectionnes Array of selected module IDs
     * @return string|false Demo URL if successful, false if error
     */
    public function createDemo($site, $modules_selectionnes)
    {
        global $conf, $langs;

        // Générer un nom unique pour la démo
        $demo_name = 'demo_' . $site->fk_projet . '_' . $site->rowid . '_' . date('YmdHis');

        // Récupérer les informations détaillées des modules et constantes
        $demo_config = $this->prepareDemoConfiguration($site, $modules_selectionnes);

        // Méthode 1: Configurer une instance Dolibarr existante (recommandé)
        if ($this->canConfigureDolibarrInstance()) {
            return $this->createRealDolibarrDemo($site, $demo_name, $demo_config);
        }

        // Méthode 2: Créer une démo locale complète
        if ($this->canCreateLocalDemo()) {
            return $this->createLocalDemo($site, $demo_name, $demo_config);
        }

        // Méthode 3: Utiliser Docker si disponible
        if ($this->isDockerAvailable()) {
            return $this->createDockerDemo($site, $demo_name, $demo_config);
        }

        // Méthode 4: Créer une page de présentation (fallback)
        return $this->createPresentationDemo($site, $demo_name, $demo_config);
    }

    /**
     * Prepare demo configuration with modules and constants details
     *
     * @param Site $site Site object
     * @param array $modules_selectionnes Selected module IDs
     * @return array Demo configuration
     */
    private function prepareDemoConfiguration($site, $modules_selectionnes)
    {
        $config = array(
            'site' => $site,
            'modules' => array(),
            'constantes' => array(),
            'extrafields' => array(),
            'devspe' => array(),
            'parametres' => array()
        );

        // Récupérer les détails des modules sélectionnés
        if (!empty($modules_selectionnes)) {
            foreach ($modules_selectionnes as $module_id) {
                $module_info = $this->getModuleInfo($module_id);
                if ($module_info) {
                    $config['modules'][$module_id] = $module_info;
                }
            }
        }

        // Récupérer la configuration du site (modules, constantes, etc.)
        $parametrage = new ParametrageSite($this->db);
        $parametrage->fetch($site->rowid);

        if (!empty($parametrage->modules)) {
            foreach ($parametrage->modules as $module_id => $module_data) {
                if (isset($module_data['checked']) && $module_data['checked'] == 1) {
                    // Récupérer les constantes activées pour ce module
                    $config['constantes'][$module_id] = $this->getModuleConstants($module_id, $module_data);

                    // Récupérer les extrafields
                    if (isset($module_data['extrafields'])) {
                        $config['extrafields'][$module_id] = $module_data['extrafields'];
                    }

                    // Récupérer les dev spé
                    if (isset($module_data['devspe'])) {
                        $config['devspe'][$module_id] = $module_data['devspe'];
                    }

                    // Récupérer les paramètres
                    if (isset($module_data['param'])) {
                        $config['parametres'][$module_id] = $module_data['param'];
                    }
                }
            }
        }

        return $config;
    }

    /**
     * Check if we can create a local demo
     *
     * @return bool True if local demo can be created
     */
    private function canCreateLocalDemo()
    {
        global $conf;

        // Vérifier si on a les permissions d'écriture
        $demos_dir = DOL_DATA_ROOT . '/demos/';
        return is_writable(dirname($demos_dir)) || is_writable($demos_dir);
    }

    /**
     * Get module information
     *
     * @param int $module_id Module ID
     * @return array|false Module info or false if not found
     */
    private function getModuleInfo($module_id)
    {
        $sql = "SELECT rowid, libelle, description, fk_logiciel
                FROM " . MAIN_DB_PREFIX . "avimm_constante_module
                WHERE rowid = " . (int)$module_id;

        $resql = $this->db->query($sql);
        if ($resql && $this->db->num_rows($resql) > 0) {
            return $this->db->fetch_array($resql);
        }

        return false;
    }

    /**
     * Get module constants
     *
     * @param int $module_id Module ID
     * @param array $module_data Module data from parametrage
     * @return array Constants information
     */
    private function getModuleConstants($module_id, $module_data)
    {
        $constants = array();

        // Récupérer toutes les constantes du module
        $sql = "SELECT c.rowid, c.libelle, c.description
                FROM " . MAIN_DB_PREFIX . "avimm_constante c
                INNER JOIN " . MAIN_DB_PREFIX . "avimm_constante_inmodule ci ON c.rowid = ci.fk_constante
                WHERE ci.fk_module = " . (int)$module_id;

        $resql = $this->db->query($sql);
        if ($resql) {
            while ($obj = $this->db->fetch_object($resql)) {
                $constants[$obj->rowid] = array(
                    'libelle' => $obj->libelle,
                    'description' => $obj->description,
                    'checked' => isset($module_data[$obj->rowid]['checked']) ? $module_data[$obj->rowid]['checked'] : 0
                );
            }
        }

        return $constants;
    }

    /**
     * Create a local demo
     *
     * @param Site $site Site object
     * @param string $demo_name Demo name
     * @param array $demo_config Demo configuration
     * @return string|false Demo URL if successful, false if error
     */
    private function createLocalDemo($site, $demo_name, $demo_config)
    {
        // Créer une page de présentation détaillée avec possibilité de lancer une vraie démo
        return $this->createPresentationDemo($site, $demo_name, $demo_config, true);
    }

    /**
     * Create a presentation demo
     *
     * @param Site $site Site object
     * @param string $demo_name Demo name
     * @param array $demo_config Demo configuration
     * @param bool $with_launch_option Include launch option
     * @return string|false Demo URL if successful, false if error
     */
    private function createPresentationDemo($site, $demo_name, $demo_config, $with_launch_option = false)
    {
        // Créer le répertoire de démo
        $demos_dir = DOL_DATA_ROOT . '/demos/';
        if (!is_dir($demos_dir)) {
            dol_mkdir($demos_dir);
        }

        $demo_path = $demos_dir . $demo_name;
        if (!dol_mkdir($demo_path)) {
            $this->error = "Impossible de créer le répertoire de démo";
            return false;
        }

        // Générer le contenu de la démo
        $demo_content = $this->generateAdvancedDemoContent($site, $demo_config, $with_launch_option);

        $index_file = $demo_path . '/index.php';
        if (file_put_contents($index_file, $demo_content) === false) {
            $this->error = "Impossible de créer le fichier de démo";
            return false;
        }

        // Créer un fichier .htaccess pour la sécurité
        $htaccess_content = "# Démo Dolibarr - Accès contrôlé\n";
        $htaccess_content .= "DirectoryIndex index.php\n";
        $htaccess_content .= "Options -Indexes\n";

        file_put_contents($demo_path . '/.htaccess', $htaccess_content);

        // URL de la démo
        $demo_url = DOL_URL_ROOT . '/document.php?modulepart=demos&file=' . $demo_name . '/index.php';

        // Enregistrer les informations de la démo
        $this->saveDemoInfo($site->fk_projet, $demo_name, $demo_url, array_keys($demo_config['modules']));

        return $demo_url;
    }

    /**
     * Generate advanced demo content
     *
     * @param Site $site Site object
     * @param array $demo_config Demo configuration
     * @param bool $with_launch_option Include launch option
     * @return string Demo content
     */
    private function generateAdvancedDemoContent($site, $demo_config, $with_launch_option = false)
    {
        global $conf;

        $content = "<?php\n";
        $content .= "// Démo Dolibarr générée automatiquement\n";
        $content .= "// Date: " . date('Y-m-d H:i:s') . "\n";
        $content .= "// Site: " . $site->nom . " (ID: " . $site->rowid . ")\n";
        $content .= "// Projet: " . $site->fk_projet . "\n";
        $content .= "?>\n";
        $content .= "<!DOCTYPE html>\n";
        $content .= "<html lang=\"fr\">\n";
        $content .= "<head>\n";
        $content .= "    <meta charset=\"UTF-8\">\n";
        $content .= "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n";
        $content .= "    <title>Démo Dolibarr - " . htmlspecialchars($site->nom) . "</title>\n";
        $content .= "    <style>\n";
        $content .= "        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }\n";
        $content .= "        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }\n";
        $content .= "        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; }\n";
        $content .= "        .header h1 { margin: 0; font-size: 2.5em; }\n";
        $content .= "        .header p { margin: 10px 0 0 0; opacity: 0.9; }\n";
        $content .= "        .content { padding: 30px; }\n";
        $content .= "        .section { margin-bottom: 30px; }\n";
        $content .= "        .section h2 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }\n";
        $content .= "        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }\n";
        $content .= "        .info-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }\n";
        $content .= "        .info-card h3 { margin-top: 0; color: #667eea; }\n";
        $content .= "        .modules-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }\n";
        $content .= "        .module-card { background: #fff; border: 1px solid #e0e0e0; border-radius: 8px; padding: 15px; transition: transform 0.2s; }\n";
        $content .= "        .module-card:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }\n";
        $content .= "        .module-card.active { border-color: #28a745; background: #f8fff9; }\n";
        $content .= "        .module-card h4 { margin: 0 0 10px 0; color: #333; }\n";
        $content .= "        .badge { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }\n";
        $content .= "        .badge.active { background: #28a745; color: white; }\n";
        $content .= "        .badge.inactive { background: #6c757d; color: white; }\n";
        $content .= "        .constants-list { margin-top: 10px; }\n";
        $content .= "        .constant-item { background: #e9ecef; padding: 5px 10px; margin: 2px 0; border-radius: 4px; font-size: 0.9em; }\n";
        $content .= "        .constant-item.active { background: #d4edda; color: #155724; }\n";
        $content .= "        .launch-section { background: #e8f4fd; padding: 20px; border-radius: 8px; text-align: center; margin-top: 30px; }\n";
        $content .= "        .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: background 0.3s; }\n";
        $content .= "        .btn:hover { background: #5a6fd8; }\n";
        $content .= "        .btn.secondary { background: #6c757d; }\n";
        $content .= "        .btn.secondary:hover { background: #5a6268; }\n";
        $content .= "        .stats { display: flex; justify-content: space-around; text-align: center; margin: 20px 0; }\n";
        $content .= "        .stat { }\n";
        $content .= "        .stat-number { font-size: 2em; font-weight: bold; color: #667eea; }\n";
        $content .= "        .stat-label { color: #666; }\n";
        $content .= "    </style>\n";
        $content .= "</head>\n";
        $content .= "<body>\n";
        $content .= "    <div class=\"container\">\n";
        $content .= "        <div class=\"header\">\n";
        $content .= "            <h1>🚀 Démo Dolibarr</h1>\n";
        $content .= "            <p>Configuration personnalisée pour " . htmlspecialchars($site->nom) . "</p>\n";
        $content .= "        </div>\n";
        $content .= "        \n";
        $content .= "        <div class=\"content\">\n";

        // Informations du site
        $content .= "            <div class=\"section\">\n";
        $content .= "                <h2>📋 Informations du Site</h2>\n";
        $content .= "                <div class=\"info-grid\">\n";
        $content .= "                    <div class=\"info-card\">\n";
        $content .= "                        <h3>Détails du Site</h3>\n";
        $content .= "                        <p><strong>Nom:</strong> " . htmlspecialchars($site->nom) . "</p>\n";
        $content .= "                        <p><strong>Type:</strong> " . htmlspecialchars($site->type) . "</p>\n";
        $content .= "                        <p><strong>Description:</strong> " . htmlspecialchars($site->description) . "</p>\n";
        $content .= "                        <p><strong>Hébergement:</strong> " . htmlspecialchars($site->hebergement) . "</p>\n";
        $content .= "                    </div>\n";
        $content .= "                    <div class=\"info-card\">\n";
        $content .= "                        <h3>Configuration</h3>\n";
        $content .= "                        <p><strong>Nombre d'utilisateurs:</strong> " . $site->nombre_utilisateur . "</p>\n";
        $content .= "                        <p><strong>Logiciel:</strong> " . $site->getLibLogiciel($site->fk_logiciel) . "</p>\n";
        $content .= "                        <p><strong>Date de création:</strong> " . date('d/m/Y H:i') . "</p>\n";
        $content .= "                    </div>\n";
        $content .= "                </div>\n";
        $content .= "            </div>\n";

        // Statistiques
        $nb_modules = count($demo_config['modules']);
        $nb_constantes = 0;
        foreach ($demo_config['constantes'] as $constants) {
            $nb_constantes += count($constants);
        }
        $nb_extrafields = count($demo_config['extrafields']);
        $nb_devspe = count($demo_config['devspe']);

        $content .= "            <div class=\"stats\">\n";
        $content .= "                <div class=\"stat\">\n";
        $content .= "                    <div class=\"stat-number\">" . $nb_modules . "</div>\n";
        $content .= "                    <div class=\"stat-label\">Modules</div>\n";
        $content .= "                </div>\n";
        $content .= "                <div class=\"stat\">\n";
        $content .= "                    <div class=\"stat-number\">" . $nb_constantes . "</div>\n";
        $content .= "                    <div class=\"stat-label\">Constantes</div>\n";
        $content .= "                </div>\n";
        $content .= "                <div class=\"stat\">\n";
        $content .= "                    <div class=\"stat-number\">" . $nb_extrafields . "</div>\n";
        $content .= "                    <div class=\"stat-label\">Extrafields</div>\n";
        $content .= "                </div>\n";
        $content .= "                <div class=\"stat\">\n";
        $content .= "                    <div class=\"stat-number\">" . $nb_devspe . "</div>\n";
        $content .= "                    <div class=\"stat-label\">Dév. Spé.</div>\n";
        $content .= "                </div>\n";
        $content .= "            </div>\n";

        // Modules et constantes
        if (!empty($demo_config['modules']) || !empty($demo_config['constantes'])) {
            $content .= "            <div class=\"section\">\n";
            $content .= "                <h2>🔧 Modules et Constantes Configurés</h2>\n";
            $content .= "                <div class=\"modules-grid\">\n";

            foreach ($demo_config['constantes'] as $module_id => $constants) {
                $module_info = isset($demo_config['modules'][$module_id]) ? $demo_config['modules'][$module_id] : null;
                $module_name = $module_info ? $module_info['libelle'] : "Module $module_id";

                $content .= "                    <div class=\"module-card active\">\n";
                $content .= "                        <h4>" . htmlspecialchars($module_name) . " <span class=\"badge active\">ACTIVÉ</span></h4>\n";

                if (!empty($constants)) {
                    $content .= "                        <div class=\"constants-list\">\n";
                    $content .= "                            <strong>Constantes:</strong>\n";
                    foreach ($constants as $const_id => $const_info) {
                        $class = $const_info['checked'] ? 'active' : '';
                        $content .= "                            <div class=\"constant-item $class\">" . htmlspecialchars($const_info['libelle']) . "</div>\n";
                    }
                    $content .= "                        </div>\n";
                }

                $content .= "                    </div>\n";
            }

            $content .= "                </div>\n";
            $content .= "            </div>\n";
        }

        // Section de lancement
        if ($with_launch_option) {
            $content .= "            <div class=\"launch-section\">\n";
            $content .= "                <h2>🎯 Lancer la Démo</h2>\n";
            $content .= "                <p>Cette configuration est prête à être déployée. Vous pouvez :</p>\n";
            $content .= "                <div style=\"margin: 20px 0;\">\n";
            $content .= "                    <a href=\"https://demo.dolibarr.org\" target=\"_blank\" class=\"btn\">Accéder à la démo en ligne</a>\n";
            $content .= "                    <a href=\"#\" onclick=\"alert('Fonctionnalité en développement')\" class=\"btn secondary\">Créer une instance locale</a>\n";
            $content .= "                </div>\n";
            $content .= "                <p><small>La démo en ligne vous permettra de tester Dolibarr avec une configuration similaire.</small></p>\n";
            $content .= "            </div>\n";
        }

        $content .= "            <div class=\"section\">\n";
        $content .= "                <h2>📞 Support</h2>\n";
        $content .= "                <p>Pour toute question concernant cette démo ou pour obtenir une instance personnalisée, contactez notre équipe.</p>\n";
        $content .= "                <p><strong>Durée de validité:</strong> Cette démo est disponible pendant 30 jours.</p>\n";
        $content .= "            </div>\n";

        $content .= "        </div>\n";
        $content .= "    </div>\n";
        $content .= "</body>\n";
        $content .= "</html>\n";

        return $content;
    }

    /**
     * Check if Docker is available
     *
     * @return bool True if Docker is available, false otherwise
     */
    private function isDockerAvailable()
    {
        exec('docker --version 2>&1', $output, $return_code);
        return ($return_code === 0);
    }

    /**
     * Create a demo using Docker
     *
     * @param Site $site Site object
     * @param string $demo_name Demo name
     * @param array $modules_selectionnes Selected modules
     * @return string|false Demo URL if successful, false if error
     */
    private function createDockerDemo($site, $demo_name, $modules_selectionnes)
    {
        global $conf;

        // Port unique pour cette démo
        $demo_port = 8080 + $site->fk_projet;

        // Créer un docker-compose.yml pour cette démo
        $docker_compose_content = $this->generateDockerCompose($demo_name, $demo_port);

        $demo_path = $conf->global->MAIN_DATA_ROOT . '/demos/' . $demo_name;
        if (!dol_mkdir($demo_path)) {
            $this->error = "Impossible de créer le répertoire de démo";
            return false;
        }

        $docker_compose_file = $demo_path . '/docker-compose.yml';
        if (file_put_contents($docker_compose_file, $docker_compose_content) === false) {
            $this->error = "Impossible de créer le fichier docker-compose.yml";
            return false;
        }

        // Lancer le conteneur Docker
        $cmd = "cd " . escapeshellarg($demo_path) . " && docker-compose up -d";
        exec($cmd, $output, $return_code);

        if ($return_code !== 0) {
            $this->error = "Erreur lors du lancement du conteneur Docker";
            return false;
        }

        // Attendre que le conteneur soit prêt
        sleep(10);

        // Configurer les modules sélectionnés
        $this->configureDockerModules($demo_name, $demo_port, $modules_selectionnes);

        $demo_url = 'http://' . $_SERVER['HTTP_HOST'] . ':' . $demo_port;

        // Enregistrer les informations de la démo
        $this->saveDemoInfo($site->fk_projet, $demo_name, $demo_url, $modules_selectionnes);

        return $demo_url;
    }

    /**
     * Create a demo using subdirectory method
     *
     * @param Site $site Site object
     * @param string $demo_name Demo name
     * @param array $modules_selectionnes Selected modules
     * @return string|false Demo URL if successful, false if error
     */
    private function createSubdirectoryDemo($site, $demo_name, $modules_selectionnes)
    {
        global $conf;

        // Créer un répertoire de démo accessible via le web
        $web_root = $_SERVER['DOCUMENT_ROOT'];
        $demo_web_path = $web_root . '/demos/' . $demo_name;

        if (!dol_mkdir($demo_web_path)) {
            $this->error = "Impossible de créer le répertoire de démo web";
            return false;
        }

        // Créer un fichier index.php qui redirige vers une démo en ligne
        $demo_content = $this->generateDemoRedirect($site, $modules_selectionnes);

        $index_file = $demo_web_path . '/index.php';
        if (file_put_contents($index_file, $demo_content) === false) {
            $this->error = "Impossible de créer le fichier de démo";
            return false;
        }

        $demo_url = 'http://' . $_SERVER['HTTP_HOST'] . '/demos/' . $demo_name;

        // Enregistrer les informations de la démo
        $this->saveDemoInfo($site->fk_projet, $demo_name, $demo_url, $modules_selectionnes);

        return $demo_url;
    }

    /**
     * Generate Docker Compose configuration
     *
     * @param string $demo_name Demo name
     * @param int $demo_port Demo port
     * @return string Docker Compose content
     */
    private function generateDockerCompose($demo_name, $demo_port)
    {
        $content = "version: '3.8'\n";
        $content .= "services:\n";
        $content .= "  dolibarr:\n";
        $content .= "    image: dolibarr/dolibarr:latest\n";
        $content .= "    container_name: " . $demo_name . "\n";
        $content .= "    ports:\n";
        $content .= "      - \"" . $demo_port . ":80\"\n";
        $content .= "    environment:\n";
        $content .= "      - DOLI_DB_HOST=db\n";
        $content .= "      - DOLI_DB_NAME=dolibarr\n";
        $content .= "      - DOLI_DB_USER=dolibarr\n";
        $content .= "      - DOLI_DB_PASSWORD=dolibarr\n";
        $content .= "      - DOLI_ADMIN_LOGIN=admin\n";
        $content .= "      - DOLI_ADMIN_PASSWORD=admin\n";
        $content .= "    depends_on:\n";
        $content .= "      - db\n";
        $content .= "    volumes:\n";
        $content .= "      - dolibarr_data:/var/www/html\n";
        $content .= "      - dolibarr_docs:/var/www/documents\n";
        $content .= "\n";
        $content .= "  db:\n";
        $content .= "    image: mysql:8.0\n";
        $content .= "    container_name: " . $demo_name . "_db\n";
        $content .= "    environment:\n";
        $content .= "      - MYSQL_ROOT_PASSWORD=root\n";
        $content .= "      - MYSQL_DATABASE=dolibarr\n";
        $content .= "      - MYSQL_USER=dolibarr\n";
        $content .= "      - MYSQL_PASSWORD=dolibarr\n";
        $content .= "    volumes:\n";
        $content .= "      - db_data:/var/lib/mysql\n";
        $content .= "\n";
        $content .= "volumes:\n";
        $content .= "  dolibarr_data:\n";
        $content .= "  dolibarr_docs:\n";
        $content .= "  db_data:\n";

        return $content;
    }

    /**
     * Configure Docker modules
     *
     * @param string $demo_name Demo name
     * @param int $demo_port Demo port
     * @param array $modules_selectionnes Selected modules
     * @return bool True if successful, false if error
     */
    private function configureDockerModules($demo_name, $demo_port, $modules_selectionnes)
    {
        // TODO: Implémenter la configuration des modules via API REST ou commandes Docker
        // Pour l'instant, on retourne true
        return true;
    }

    /**
     * Generate demo redirect content
     *
     * @param Site $site Site object
     * @param array $modules_selectionnes Selected modules
     * @return string Demo redirect content
     */
    private function generateDemoRedirect($site, $modules_selectionnes)
    {
        global $langs;

        $content = "<?php\n";
        $content .= "// Démo générée automatiquement le " . date('Y-m-d H:i:s') . "\n";
        $content .= "// Projet: " . $site->fk_projet . "\n";
        $content .= "// Site: " . $site->nom . "\n";
        $content .= "// Modules sélectionnés: " . implode(', ', $modules_selectionnes) . "\n";
        $content .= "?>\n";
        $content .= "<!DOCTYPE html>\n";
        $content .= "<html>\n";
        $content .= "<head>\n";
        $content .= "    <title>Démo Dolibarr - " . htmlspecialchars($site->nom) . "</title>\n";
        $content .= "    <meta charset=\"UTF-8\">\n";
        $content .= "    <style>\n";
        $content .= "        body { font-family: Arial, sans-serif; margin: 50px; }\n";
        $content .= "        .demo-info { background: #f0f0f0; padding: 20px; border-radius: 5px; }\n";
        $content .= "        .modules-list { margin-top: 20px; }\n";
        $content .= "        .module-item { background: #e0e0e0; padding: 10px; margin: 5px 0; border-radius: 3px; }\n";
        $content .= "    </style>\n";
        $content .= "</head>\n";
        $content .= "<body>\n";
        $content .= "    <h1>Démo Dolibarr - " . htmlspecialchars($site->nom) . "</h1>\n";
        $content .= "    <div class=\"demo-info\">\n";
        $content .= "        <h2>Informations de la démo</h2>\n";
        $content .= "        <p><strong>Projet:</strong> " . $site->fk_projet . "</p>\n";
        $content .= "        <p><strong>Site:</strong> " . htmlspecialchars($site->nom) . "</p>\n";
        $content .= "        <p><strong>Type:</strong> " . htmlspecialchars($site->type) . "</p>\n";
        $content .= "        <p><strong>Date de création:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
        $content .= "        <div class=\"modules-list\">\n";
        $content .= "            <h3>Modules sélectionnés:</h3>\n";

        if (count($modules_selectionnes) > 0) {
            foreach ($modules_selectionnes as $module_id) {
                $content .= "            <div class=\"module-item\">Module ID: " . $module_id . "</div>\n";
            }
        } else {
            $content .= "            <p>Aucun module sélectionné</p>\n";
        }

        $content .= "        </div>\n";
        $content .= "        <p><strong>Note:</strong> Cette démo sera disponible pendant 30 jours.</p>\n";
        $content .= "        <p><a href=\"https://demo.dolibarr.org\" target=\"_blank\">Accéder à la démo Dolibarr en ligne</a></p>\n";
        $content .= "    </div>\n";
        $content .= "</body>\n";
        $content .= "</html>\n";

        return $content;
    }

    /**
     * Copy Dolibarr files to demo directory
     *
     * @param string $source Source directory
     * @param string $destination Destination directory
     * @return bool True if successful, false if error
     */
    private function copyDolibarrFiles($source, $destination)
    {
        // Liste des fichiers/dossiers essentiels à copier
        $essential_files = array(
            'htdocs',
            'scripts'
        );

        foreach ($essential_files as $file) {
            $source_path = $source . '/' . $file;
            $dest_path = $destination . '/' . $file;

            if (is_dir($source_path)) {
                if (!$this->recursiveCopy($source_path, $dest_path)) {
                    return false;
                }
            } elseif (is_file($source_path)) {
                if (!copy($source_path, $dest_path)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Recursive copy function
     *
     * @param string $src Source directory
     * @param string $dst Destination directory
     * @return bool True if successful, false if error
     */
    private function recursiveCopy($src, $dst)
    {
        $dir = opendir($src);
        if (!$dir) return false;

        if (!dol_mkdir($dst)) return false;

        while (($file = readdir($dir)) !== false) {
            if ($file != '.' && $file != '..') {
                if (is_dir($src . '/' . $file)) {
                    if (!$this->recursiveCopy($src . '/' . $file, $dst . '/' . $file)) {
                        return false;
                    }
                } else {
                    if (!copy($src . '/' . $file, $dst . '/' . $file)) {
                        return false;
                    }
                }
            }
        }

        closedir($dir);
        return true;
    }

    /**
     * Create demo database
     *
     * @param string $db_name Database name
     * @return bool True if successful, false if error
     */
    private function createDemoDatabase($db_name)
    {
        global $conf;

        // Créer une nouvelle base de données
        $sql = "CREATE DATABASE IF NOT EXISTS " . $db_name . " CHARACTER SET utf8 COLLATE utf8_unicode_ci";
        $resql = $this->db->query($sql);

        if (!$resql) {
            return false;
        }

        // Copier la structure de la base de données actuelle
        $current_db = $conf->db->name;

        // Exporter la structure
        $export_cmd = "mysqldump -h " . $conf->db->host . " -u " . $conf->db->user . " -p" . $conf->db->pass . " --no-data " . $current_db . " | mysql -h " . $conf->db->host . " -u " . $conf->db->user . " -p" . $conf->db->pass . " " . $db_name;

        // Exécuter la commande (attention : ceci nécessite que mysqldump soit disponible)
        exec($export_cmd, $output, $return_code);

        return ($return_code === 0);
    }

    /**
     * Configure Dolibarr with selected modules
     *
     * @param string $demo_path Demo installation path
     * @param string $db_name Database name
     * @param array $modules_selectionnes Selected modules
     * @return bool True if successful, false if error
     */
    private function configureDolibarr($demo_path, $db_name, $modules_selectionnes)
    {
        global $conf;

        // Créer le fichier de configuration conf.php
        $conf_content = $this->generateConfFile($db_name);

        $conf_file = $demo_path . '/htdocs/conf/conf.php';
        if (!dol_mkdir(dirname($conf_file))) {
            return false;
        }

        if (file_put_contents($conf_file, $conf_content) === false) {
            return false;
        }

        // Activer les modules sélectionnés
        return $this->activateModules($db_name, $modules_selectionnes);
    }

    /**
     * Generate configuration file content
     *
     * @param string $db_name Database name
     * @return string Configuration file content
     */
    private function generateConfFile($db_name)
    {
        global $conf;

        $conf_content = "<?php\n";
        $conf_content .= "// Dolibarr demo configuration file\n";
        $conf_content .= "// Generated automatically on " . date('Y-m-d H:i:s') . "\n\n";
        $conf_content .= "\$dolibarr_main_url_root='http://" . $_SERVER['HTTP_HOST'] . "/demos/" . basename(dirname(dirname(__FILE__))) . "/htdocs';\n";
        $conf_content .= "\$dolibarr_main_document_root='" . dirname(__FILE__) . "/htdocs';\n";
        $conf_content .= "\$dolibarr_main_url_root_alt='/custom';\n";
        $conf_content .= "\$dolibarr_main_document_root_alt='" . dirname(__FILE__) . "/htdocs/custom';\n";
        $conf_content .= "\$dolibarr_main_data_root='" . dirname(__FILE__) . "/documents';\n";
        $conf_content .= "\$dolibarr_main_db_host='" . $conf->db->host . "';\n";
        $conf_content .= "\$dolibarr_main_db_port='" . $conf->db->port . "';\n";
        $conf_content .= "\$dolibarr_main_db_name='" . $db_name . "';\n";
        $conf_content .= "\$dolibarr_main_db_prefix='" . $conf->db->prefix . "';\n";
        $conf_content .= "\$dolibarr_main_db_user='" . $conf->db->user . "';\n";
        $conf_content .= "\$dolibarr_main_db_pass='" . $conf->db->pass . "';\n";
        $conf_content .= "\$dolibarr_main_db_type='" . $conf->db->type . "';\n";
        $conf_content .= "\$dolibarr_main_db_character_set='utf8';\n";
        $conf_content .= "\$dolibarr_main_db_collation='utf8_unicode_ci';\n";
        $conf_content .= "\$dolibarr_main_authentication='dolibarr';\n";
        $conf_content .= "\$dolibarr_main_prod='1';\n";
        $conf_content .= "\$dolibarr_main_force_https='0';\n";
        $conf_content .= "\$dolibarr_main_restrict_os_commands='mysqldump, mysql, pg_dump, pgrestore';\n";
        $conf_content .= "\$dolibarr_nocsrfcheck='0';\n";
        $conf_content .= "\$dolibarr_main_instance_unique_id='" . uniqid() . "';\n";
        $conf_content .= "\$dolibarr_mailing_limit_sendbyweb='0';\n";

        return $conf_content;
    }

    /**
     * Activate selected modules in demo database
     *
     * @param string $db_name Database name
     * @param array $modules_selectionnes Selected modules
     * @return bool True if successful, false if error
     */
    private function activateModules($db_name, $modules_selectionnes)
    {
        // TODO: Implémenter l'activation des modules dans la base de données de démo
        // Ceci nécessiterait de se connecter à la base de données de démo
        // et d'insérer les enregistrements appropriés dans la table llx_const

        return true;
    }

    /**
     * Create demo URL
     *
     * @param string $demo_name Demo name
     * @param string $demo_path Demo path
     * @return string Demo URL
     */
    private function createDemoUrl($demo_name, $demo_path)
    {
        // Créer un lien symbolique ou configurer un virtual host
        // Pour simplifier, on retourne une URL basée sur le nom de la démo
        return 'http://' . $_SERVER['HTTP_HOST'] . '/demos/' . $demo_name . '/htdocs/';
    }

    /**
     * Save demo information
     *
     * @param int $projet_id Project ID
     * @param string $demo_name Demo name
     * @param string $demo_url Demo URL
     * @param array $modules_selectionnes Selected modules
     * @return bool True if successful, false if error
     */
    private function saveDemoInfo($projet_id, $demo_name, $demo_url, $modules_selectionnes)
    {
        $sql = "INSERT INTO " . MAIN_DB_PREFIX . "rendez_vous_demo (";
        $sql .= "fk_projet, demo_name, demo_url, modules_selectionnes, date_creation";
        $sql .= ") VALUES (";
        $sql .= $projet_id . ", ";
        $sql .= "'" . $this->db->escape($demo_name) . "', ";
        $sql .= "'" . $this->db->escape($demo_url) . "', ";
        $sql .= "'" . $this->db->escape(json_encode($modules_selectionnes)) . "', ";
        $sql .= "'" . $this->db->idate(dol_now()) . "'";
        $sql .= ")";

        $resql = $this->db->query($sql);
        return ($resql !== false);
    }

    /**
     * Check if we can configure a Dolibarr instance
     *
     * @return bool True if we can configure an instance
     */
    private function canConfigureDolibarrInstance()
    {
        global $conf;

        // Vérifier si les paramètres de l'instance cible sont configurés
        return !empty($conf->global->RENDEZVOUSCLIENT_DEMO_DB_HOST) &&
               !empty($conf->global->RENDEZVOUSCLIENT_DEMO_DB_NAME) &&
               !empty($conf->global->RENDEZVOUSCLIENT_DEMO_DB_USER) &&
               !empty($conf->global->RENDEZVOUSCLIENT_DEMO_URL);
    }

    /**
     * Create a real Dolibarr demo by configuring an existing instance
     *
     * @param Site $site Site object
     * @param string $demo_name Demo name
     * @param array $demo_config Demo configuration
     * @return string|false Demo URL if successful, false if error
     */
    private function createRealDolibarrDemo($site, $demo_name, $demo_config)
    {
        global $conf;

        // Configuration de l'instance cible
        $target_config = array(
            'type' => 'mysqli',
            'host' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_HOST,
            'database' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_NAME,
            'user' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_USER,
            'password' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PASSWORD ?? '',
            'port' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PORT ?? 3306,
            'prefix' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PREFIX ?? 'llx_',
            'url' => $conf->global->RENDEZVOUSCLIENT_DEMO_URL
        );

        // Créer le gestionnaire Dolibarr
        $dolibarr_manager = new DolibarrManager($this->db);

        if (!$dolibarr_manager->setTargetInstance($target_config)) {
            $this->error = "Configuration de l'instance cible invalide: " . $dolibarr_manager->error;
            return false;
        }

        // Configurer l'instance Dolibarr
        $result = $dolibarr_manager->configureDolibarrInstance($site, $demo_config['constantes']);

        if (!$result['success']) {
            $this->error = "Erreur lors de la configuration de l'instance: " . implode(', ', $result['errors']);
            return false;
        }

        // Créer une page de présentation avec lien vers l'instance réelle
        $presentation_url = $this->createPresentationDemo($site, $demo_name, $demo_config, true);

        // Enregistrer les informations de la démo
        $this->saveDemoInfo($site->fk_projet, $demo_name, $target_config['url'], array_keys($demo_config['modules']));

        return $presentation_url;
    }
}
