<?php
/**
 *	\file       rendezvousclient/setup_generation.php
 *	\ingroup    rendezvousclient
 *	\brief      Configuration initiale du système de génération
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'setup_all') {
    $results = array();
    
    // 1. Créer les répertoires nécessaires
    $directories = array(
        'Templates' => DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/',
        'Output' => DOL_DATA_ROOT.'/rendezvousclient/',
        'Sites' => DOL_DATA_ROOT.'/rendezvousclient/sites/'
    );
    
    foreach ($directories as $name => $path) {
        if (!is_dir($path)) {
            if (dol_mkdir($path)) {
                $results[] = array('action' => "Création répertoire $name", 'status' => 'OK', 'details' => $path);
            } else {
                $results[] = array('action' => "Création répertoire $name", 'status' => 'ERREUR', 'details' => "Impossible de créer $path");
            }
        } else {
            $results[] = array('action' => "Répertoire $name", 'status' => 'OK', 'details' => 'Existe déjà');
        }
    }
    
    // 2. Créer les templates par défaut
    $templates_dir = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/';
    
    $templates = array(
        'cahier_des_charges_template.odt' => 'Cahier des Charges',
        'proposition_technique_template.odt' => 'Proposition Technique et Commerciale',
        'devis_template.odt' => 'Devis',
        'synthese_cdc_template.odt' => 'Synthèse du Cahier des Charges'
    );
    
    foreach ($templates as $filename => $title) {
        $template_path = $templates_dir . $filename;
        
        if (!file_exists($template_path)) {
            if (createDefaultTemplate($filename, $title, $template_path)) {
                $results[] = array('action' => "Création template $title", 'status' => 'OK', 'details' => $filename);
            } else {
                $results[] = array('action' => "Création template $title", 'status' => 'ERREUR', 'details' => "Impossible de créer $filename");
            }
        } else {
            $results[] = array('action' => "Template $title", 'status' => 'OK', 'details' => 'Existe déjà');
        }
    }
    
    // 3. Tester la création d'un fichier ODT
    try {
        $test_file = DOL_DATA_ROOT.'/rendezvousclient/test_setup.odt';
        
        $zip = new ZipArchive();
        if ($zip->open($test_file, ZipArchive::CREATE) === TRUE) {
            $zip->addFromString('mimetype', 'application/vnd.oasis.opendocument.text');
            $zip->addFromString('content.xml', '<?xml version="1.0" encoding="UTF-8"?><office:document-content xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0"><office:body><office:text><text:p>Test de configuration</text:p></office:text></office:body></office:document-content>');
            $zip->close();
            
            if (file_exists($test_file)) {
                $results[] = array('action' => 'Test création ODT', 'status' => 'OK', 'details' => 'Fichier créé avec succès');
                unlink($test_file); // Nettoyer
            } else {
                $results[] = array('action' => 'Test création ODT', 'status' => 'ERREUR', 'details' => 'Fichier non créé');
            }
        } else {
            $results[] = array('action' => 'Test création ODT', 'status' => 'ERREUR', 'details' => 'Impossible d\'ouvrir le fichier ZIP');
        }
    } catch (Exception $e) {
        $results[] = array('action' => 'Test création ODT', 'status' => 'ERREUR', 'details' => $e->getMessage());
    }
    
    // Compter les succès
    $success_count = 0;
    $error_count = 0;
    foreach ($results as $result) {
        if ($result['status'] == 'OK') {
            $success_count++;
        } else {
            $error_count++;
        }
    }
    
    if ($error_count == 0) {
        setEventMessages("Configuration terminée avec succès ! ($success_count actions réussies)", null, 'mesgs');
    } else {
        setEventMessages("Configuration terminée avec $error_count erreur(s) sur ".count($results)." actions", null, 'warnings');
    }
}

/**
 * Crée un template par défaut
 */
function createDefaultTemplate($filename, $title, $template_path)
{
    $content_xml = '<?xml version="1.0" encoding="UTF-8"?>
<office:document-content xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0" xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0" xmlns:fo="urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0">
<office:automatic-styles>
<style:style style:name="Heading_20_1" style:display-name="Heading 1" style:family="paragraph" style:parent-style-name="Heading" style:next-style-name="Text_20_body" style:class="text">
<style:paragraph-properties fo:margin-top="0.423cm" fo:margin-bottom="0.212cm" fo:keep-with-next="always"/>
<style:text-properties fo:font-size="18pt" fo:font-weight="bold"/>
</style:style>
<style:style style:name="Heading_20_2" style:display-name="Heading 2" style:family="paragraph" style:parent-style-name="Heading" style:next-style-name="Text_20_body" style:class="text">
<style:paragraph-properties fo:margin-top="0.353cm" fo:margin-bottom="0.212cm" fo:keep-with-next="always"/>
<style:text-properties fo:font-size="14pt" fo:font-weight="bold"/>
</style:style>
<style:style style:name="Standard" style:family="paragraph" style:class="text">
<style:paragraph-properties fo:margin-top="0cm" fo:margin-bottom="0.212cm"/>
</style:style>
</office:automatic-styles>
<office:body>
<office:text>
<text:h text:style-name="Heading_20_1" text:outline-level="1">'.$title.'</text:h>
<text:p text:style-name="Standard"/>
<text:p text:style-name="Standard">Document généré le : __DATE_GENERATION__</text:p>
<text:p text:style-name="Standard">Généré par : __UTILISATEUR__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Client</text:h>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Société :</text:span> __SOCIETE_NOM__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Adresse :</text:span> __SOCIETE_ADRESSE__</text:p>
<text:p text:style-name="Standard">__SOCIETE_CP__ __SOCIETE_VILLE__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Téléphone :</text:span> __SOCIETE_TEL__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Email :</text:span> __SOCIETE_EMAIL__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Projet</text:h>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Référence :</text:span> __PROJET_REF__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Titre :</text:span> __PROJET_TITRE__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Description :</text:span> __PROJET_DESCRIPTION__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Site</text:h>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Nom du site :</text:span> __SITE_NOM__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Type :</text:span> __SITE_TYPE__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Description :</text:span> __SITE_DESCRIPTION__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Nombre d\'utilisateurs :</text:span> __SITE_NB_UTILISATEURS__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Logiciel :</text:span> __SITE_LOGICIEL__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Hébergement :</text:span> __SITE_HEBERGEMENT__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Utilisateurs du Site</text:h>
<text:p text:style-name="Standard">__SITE_UTILISATEURS__</text:p>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Entreprise</text:h>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Entreprise :</text:span> __ENTREPRISE_NOM__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Adresse :</text:span> __ENTREPRISE_ADRESSE__</text:p>
<text:p text:style-name="Standard">__ENTREPRISE_CP__ __ENTREPRISE_VILLE__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Téléphone :</text:span> __ENTREPRISE_TEL__</text:p>
<text:p text:style-name="Standard"><text:span text:style-name="Bold">Email :</text:span> __ENTREPRISE_EMAIL__</text:p>

</office:text>
</office:body>
</office:document-content>';
    
    // Créer un fichier ZIP (ODT)
    $zip = new ZipArchive();
    if ($zip->open($template_path, ZipArchive::CREATE) !== TRUE) {
        return false;
    }
    
    // Ajouter les fichiers nécessaires
    $zip->addFromString('mimetype', 'application/vnd.oasis.opendocument.text');
    $zip->addFromString('content.xml', $content_xml);
    
    // META-INF/manifest.xml
    $manifest = '<?xml version="1.0" encoding="UTF-8"?>
<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
<manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.text"/>
<manifest:file-entry manifest:full-path="content.xml" manifest:media-type="text/xml"/>
</manifest:manifest>';
    
    $zip->addFromString('META-INF/manifest.xml', $manifest);
    
    $zip->close();
    
    return true;
}

/*
 * View
 */

$title = 'Configuration du système de génération de documents';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Cette page configure automatiquement tout ce qui est nécessaire pour la génération de documents ODT.';
print '</div>';

// Vérification des prérequis
print '<h3>Prérequis</h3>';

$prerequisites = array();

// ZipArchive
$prerequisites[] = array(
    'test' => 'Extension ZipArchive',
    'status' => class_exists('ZipArchive') ? 'OK' : 'ERREUR',
    'details' => class_exists('ZipArchive') ? 'Extension disponible' : 'Extension manquante - contactez votre administrateur'
);

// Permissions d'écriture
$write_test_dir = DOL_DATA_ROOT.'/rendezvousclient/';
$can_write = is_dir($write_test_dir) || dol_mkdir($write_test_dir);
$prerequisites[] = array(
    'test' => 'Permissions d\'écriture',
    'status' => $can_write ? 'OK' : 'ERREUR',
    'details' => $can_write ? 'Permissions OK' : 'Impossible d\'écrire dans '.DOL_DATA_ROOT
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Prérequis</th>';
print '<th>Statut</th>';
print '<th>Détails</th>';
print '</tr>';

$can_proceed = true;
foreach ($prerequisites as $prereq) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$prereq['test'].'</strong></td>';
    print '<td>';
    if ($prereq['status'] == 'OK') {
        print '<span class="badge badge-status4 badge-status">OK</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">ERREUR</span>';
        $can_proceed = false;
    }
    print '</td>';
    print '<td>'.$prereq['details'].'</td>';
    print '</tr>';
}

print '</table>';

if ($can_proceed) {
    if ($action != 'setup_all') {
        print '<br><div class="center">';
        print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=setup_all&token='.newToken().'">Configurer automatiquement</a>';
        print '</div>';
        
        print '<br><h3>Ce qui sera configuré</h3>';
        print '<div class="ok">';
        print '<ul>';
        print '<li><strong>Répertoires :</strong> Création des répertoires templates et output</li>';
        print '<li><strong>Templates par défaut :</strong> 4 templates ODT prêts à utiliser</li>';
        print '<li><strong>Test de fonctionnement :</strong> Vérification de la création de fichiers ODT</li>';
        print '</ul>';
        print '</div>';
    } else {
        // Afficher les résultats
        if (!empty($results)) {
            print '<br><h3>Résultats de la configuration</h3>';
            
            print '<table class="border centpercent">';
            print '<tr class="liste_titre">';
            print '<th>Action</th>';
            print '<th>Statut</th>';
            print '<th>Détails</th>';
            print '</tr>';
            
            foreach ($results as $result) {
                print '<tr class="oddeven">';
                print '<td><strong>'.$result['action'].'</strong></td>';
                print '<td>';
                if ($result['status'] == 'OK') {
                    print '<span class="badge badge-status4 badge-status">OK</span>';
                } else {
                    print '<span class="badge badge-status8 badge-status">ERREUR</span>';
                }
                print '</td>';
                print '<td>'.$result['details'].'</td>';
                print '</tr>';
            }
            
            print '</table>';
        }
        
        print '<br><div class="center">';
        print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
        print '</div>';
    }
} else {
    print '<br><div class="error">';
    print '<strong>Configuration impossible !</strong><br>';
    print 'Veuillez résoudre les problèmes de prérequis avant de continuer.';
    print '</div>';
}

// Liens utiles
print '<br><h3>Étapes suivantes</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_quick_generation.php">Test rapide</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/diagnostic_generation.php">Diagnostic complet</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/admin/templates.php">Gestion templates</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
