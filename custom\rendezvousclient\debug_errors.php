<?php
/**
 *	\file       rendezvousclient/debug_errors.php
 *	\ingroup    rendezvousclient
 *	\brief      Debug des erreurs du module
 */

// Désactiver temporairement Xdebug pour éviter les erreurs en cascade
if (function_exists('xdebug_disable')) {
    xdebug_disable();
}

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'test_classes') {
    $results = array();
    
    // Test des classes principales
    $classes_to_test = array(
        'Site' => '/rendezvousclient/site/class/site.class.php',
        'ParametrageSite' => '/rendezvousclient/site/class/parametragesite.class.php',
        'DemoCreator' => '/rendezvousclient/site/class/democreator.class.php',
        'DocumentGenerator' => '/rendezvousclient/core/class/document_generator.class.php'
    );
    
    foreach ($classes_to_test as $class_name => $class_path) {
        try {
            dol_include_once($class_path);
            
            if (class_exists($class_name)) {
                // Tenter de créer une instance
                $instance = new $class_name($db);
                $results[] = array(
                    'class' => $class_name,
                    'status' => 'OK',
                    'message' => 'Classe chargée et instanciée avec succès'
                );
            } else {
                $results[] = array(
                    'class' => $class_name,
                    'status' => 'ERREUR',
                    'message' => 'Classe non trouvée après inclusion'
                );
            }
        } catch (Exception $e) {
            $results[] = array(
                'class' => $class_name,
                'status' => 'ERREUR',
                'message' => 'Exception: ' . $e->getMessage()
            );
        } catch (Error $e) {
            $results[] = array(
                'class' => $class_name,
                'status' => 'ERREUR',
                'message' => 'Erreur fatale: ' . $e->getMessage()
            );
        }
    }
}

if ($action == 'test_files') {
    $results = array();
    
    // Test des fichiers principaux
    $files_to_test = array(
        'site/list.php',
        'site/card.php',
        'site/parametrage.php',
        'demo/list.php',
        'demo/manage.php',
        'test_demo_creation.php',
        'test_parametrage.php'
    );
    
    foreach ($files_to_test as $file) {
        $full_path = DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/' . $file;
        
        if (file_exists($full_path)) {
            // Vérifier la syntaxe PHP
            $output = array();
            $return_var = 0;
            exec("php -l \"$full_path\" 2>&1", $output, $return_var);
            
            if ($return_var === 0) {
                $results[] = array(
                    'file' => $file,
                    'status' => 'OK',
                    'message' => 'Syntaxe PHP correcte'
                );
            } else {
                $results[] = array(
                    'file' => $file,
                    'status' => 'ERREUR',
                    'message' => 'Erreur de syntaxe: ' . implode(' ', $output)
                );
            }
        } else {
            $results[] = array(
                'file' => $file,
                'status' => 'ERREUR',
                'message' => 'Fichier non trouvé'
            );
        }
    }
}

if ($action == 'check_database') {
    $results = array();
    
    // Vérifier les tables nécessaires
    $tables_to_check = array(
        'rendez_vous_site',
        'rendez_vous_site_utilisateur',
        'rendez_vous_site_module',
        'rendez_vous_demo',
        'avimm_constante_module',
        'avimm_constante',
        'avimm_constante_logiciel'
    );
    
    foreach ($tables_to_check as $table) {
        $full_table = MAIN_DB_PREFIX . $table;
        
        // Vérifier si la table existe
        $sql = "SHOW TABLES LIKE '$full_table'";
        $resql = $db->query($sql);
        
        if ($resql && $db->num_rows($resql) > 0) {
            // Vérifier la structure
            $sql = "DESCRIBE $full_table";
            $resql2 = $db->query($sql);
            
            if ($resql2) {
                $nb_fields = $db->num_rows($resql2);
                $results[] = array(
                    'table' => $table,
                    'status' => 'OK',
                    'message' => "Table existe avec $nb_fields champ(s)"
                );
            } else {
                $results[] = array(
                    'table' => $table,
                    'status' => 'ERREUR',
                    'message' => 'Impossible de lire la structure'
                );
            }
        } else {
            $results[] = array(
                'table' => $table,
                'status' => 'ERREUR',
                'message' => 'Table non trouvée'
            );
        }
    }
}

/*
 * View
 */

$title = 'Debug des erreurs du module';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script aide à diagnostiquer et corriger les erreurs du module rendezvousclient.';
print '</div>';

// Informations sur l'environnement
print '<h3>🔍 Informations sur l\'environnement</h3>';

$env_info = array(
    'Version PHP' => PHP_VERSION,
    'Xdebug activé' => function_exists('xdebug_info') ? 'OUI' : 'NON',
    'Version Dolibarr' => DOL_VERSION,
    'Mode debug' => empty($conf->global->MAIN_MODULE_DOLIBARR_DEBUG) ? 'NON' : 'OUI',
    'Répertoire custom' => DOL_DOCUMENT_ROOT . '/custom/',
    'Répertoire data' => DOL_DATA_ROOT
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Information</th>';
print '<th>Valeur</th>';
print '</tr>';

foreach ($env_info as $key => $value) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$key.'</strong></td>';
    print '<td>'.$value.'</td>';
    print '</tr>';
}

print '</table>';

// Tests disponibles
print '<br><h3>🧪 Tests disponibles</h3>';

print '<div class="tabsAction">';
print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_classes&token='.newToken().'">Tester les classes</a>';
print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_files&token='.newToken().'">Tester les fichiers</a>';
print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=check_database&token='.newToken().'">Vérifier la base</a>';
print '</div>';

// Affichage des résultats
if (!empty($results)) {
    print '<br><h3>📋 Résultats des tests</h3>';
    
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Élément testé</th>';
    print '<th>Statut</th>';
    print '<th>Message</th>';
    print '</tr>';
    
    $nb_ok = 0;
    $nb_error = 0;
    
    foreach ($results as $result) {
        $element = isset($result['class']) ? $result['class'] : (isset($result['file']) ? $result['file'] : $result['table']);
        
        print '<tr class="oddeven">';
        print '<td><strong>'.$element.'</strong></td>';
        print '<td>';
        if ($result['status'] == 'OK') {
            print '<span class="badge badge-status4 badge-status">OK</span>';
            $nb_ok++;
        } else {
            print '<span class="badge badge-status8 badge-status">ERREUR</span>';
            $nb_error++;
        }
        print '</td>';
        print '<td>'.$result['message'].'</td>';
        print '</tr>';
    }
    
    print '</table>';
    
    print '<br>';
    if ($nb_error == 0) {
        print '<div class="ok">';
        print '<strong>✅ Tous les tests sont OK !</strong> ('.$nb_ok.' tests réussis)';
        print '</div>';
    } else {
        print '<div class="warning">';
        print '<strong>⚠️ '.$nb_error.' erreur(s) détectée(s)</strong> sur '.($nb_ok + $nb_error).' tests.';
        print '</div>';
    }
}

// Erreurs courantes et solutions
print '<br><h3>🔧 Erreurs courantes et solutions</h3>';

$common_errors = array(
    'Xdebug: user triggered' => array(
        'description' => 'Erreur déclenchée par Xdebug quand une fonction Dolibarr détecte un problème',
        'solutions' => array(
            'Vérifier les logs d\'erreur PHP',
            'Désactiver temporairement Xdebug',
            'Vérifier la syntaxe des fichiers PHP',
            'Contrôler les variables non définies'
        )
    ),
    'Undefined variable' => array(
        'description' => 'Variable utilisée sans être définie',
        'solutions' => array(
            'Utiliser le script fix_all_missing_variables.php',
            'Ajouter les variables manquantes en début de fichier',
            'Vérifier les vérifications isset() avant utilisation'
        )
    ),
    'Class not found' => array(
        'description' => 'Classe non trouvée ou mal incluse',
        'solutions' => array(
            'Vérifier les chemins dol_include_once()',
            'Contrôler l\'existence des fichiers de classe',
            'Vérifier la syntaxe des classes'
        )
    ),
    'Call to undefined method' => array(
        'description' => 'Méthode appelée qui n\'existe pas',
        'solutions' => array(
            'Vérifier l\'orthographe des noms de méthodes',
            'Contrôler que la classe est bien chargée',
            'Vérifier la compatibilité des versions'
        )
    )
);

foreach ($common_errors as $error => $info) {
    print '<h4>❌ '.$error.'</h4>';
    print '<p><strong>Description :</strong> '.$info['description'].'</p>';
    print '<p><strong>Solutions :</strong></p>';
    print '<ul>';
    foreach ($info['solutions'] as $solution) {
        print '<li>'.$solution.'</li>';
    }
    print '</ul>';
}

// Scripts de correction
print '<br><h3>🛠️ Scripts de correction disponibles</h3>';

$fix_scripts = array(
    'fix_all_missing_variables.php' => 'Corrige toutes les variables manquantes',
    'test_parametrage.php' => 'Teste le fichier parametrage.php',
    'test_demo_creation.php' => 'Teste la création de démos',
    'setup_generation.php' => 'Configure le système de génération',
    'diagnostic_generation.php' => 'Diagnostic complet du système'
);

print '<div class="tabsAction">';
foreach ($fix_scripts as $script => $description) {
    print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/'.$script.'" title="'.$description.'">'.$script.'</a>';
}
print '</div>';

// Conseils de débogage
print '<br><h3>💡 Conseils de débogage</h3>';

print '<div class="info">';
print '<h4>Pour résoudre l\'erreur Xdebug :</h4>';
print '<ol>';
print '<li><strong>Identifier la cause :</strong> L\'erreur Xdebug est un symptôme, pas la cause</li>';
print '<li><strong>Vérifier les logs :</strong> Regarder les logs PHP pour l\'erreur réelle</li>';
print '<li><strong>Tester les fichiers :</strong> Utiliser les scripts de test ci-dessus</li>';
print '<li><strong>Corriger progressivement :</strong> Commencer par les erreurs de variables</li>';
print '<li><strong>Désactiver Xdebug temporairement :</strong> Si nécessaire pour déboguer</li>';
print '</ol>';
print '</div>';

print '<div class="warning">';
print '<h4>⚠️ Si l\'erreur persiste :</h4>';
print '<ul>';
print '<li>Vérifiez les logs d\'erreur PHP dans votre serveur web</li>';
print '<li>Testez chaque fichier individuellement</li>';
print '<li>Utilisez les scripts de correction automatique</li>';
print '<li>Contactez le support si nécessaire</li>';
print '</ul>';
print '</div>';

// Liens utiles
print '<br><h3>🔗 Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/admin/system/dolibarr.php">Informations système</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
