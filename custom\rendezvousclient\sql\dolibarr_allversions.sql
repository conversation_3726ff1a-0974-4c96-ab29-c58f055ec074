--
-- <PERSON><PERSON><PERSON> run when an upgrade of <PERSON><PERSON><PERSON><PERSON> is done. Whatever is the Dolibarr version.
--

-- Création de la table principale des rendez-vous
CREATE TABLE IF NOT EXISTS llx_rendez_vous (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_projet integer NOT NULL,
  fk_statut tinyint DEFAULT 1,
  type tinyint DEFAULT 1,
  numero varchar(50),
  date datetime,
  date_demo datetime,
  date_livraison datetime,
  objet varchar(255),
  objectif_client text,
  besoin_client text,
  reponse_besoin_client text,
  compte_rendu text,
  datec datetime,
  fk_user integer,
  INDEX idx_fk_projet (fk_projet),
  INDEX idx_fk_statut (fk_statut),
  INDEX idx_date (date)
) ENGINE=innodb;

-- Création de la table de liaison rendez-vous / contacts
CREATE TABLE IF NOT EXISTS llx_rendez_vous_socpeople (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_rendez_vous integer NOT NULL,
  fk_socpeople integer NOT NULL,
  INDEX idx_fk_rendez_vous (fk_rendez_vous),
  INDEX idx_fk_socpeople (fk_socpeople)
) ENGINE=innodb;

-- Création de la table des sites
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_projet integer NOT NULL,
  date datetime,
  nom varchar(255),
  type varchar(100),
  description text,
  nombre_utilisateur integer,
  fk_logiciel integer,
  autre text,
  hebergement text,
  INDEX idx_fk_projet (fk_projet)
) ENGINE=innodb;

-- Création de la table des utilisateurs par site
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_utilisateur (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_rendez_vous_site integer NOT NULL,
  type varchar(100),
  descriptif text,
  INDEX idx_fk_site (fk_rendez_vous_site)
) ENGINE=innodb;

-- Création de la table des modules par site
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_module (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_site integer NOT NULL,
  fk_module integer NOT NULL,
  checked tinyint DEFAULT 0,
  INDEX idx_fk_site (fk_site),
  INDEX idx_fk_module (fk_module)
) ENGINE=innodb;

-- Création de la table des constantes par module de site
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_module_constante (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_site_module integer NOT NULL,
  fk_constante integer NOT NULL,
  checked tinyint DEFAULT 0,
  INDEX idx_fk_site_module (fk_site_module)
) ENGINE=innodb;

-- Création de la table des développements spécifiques par module
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_module_devspe (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_site_module integer NOT NULL,
  description text,
  INDEX idx_fk_site_module (fk_site_module)
) ENGINE=innodb;

-- Création de la table des paramétrages par module
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_module_param (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_site_module integer NOT NULL,
  description text,
  INDEX idx_fk_site_module (fk_site_module)
) ENGINE=innodb;

-- Création de la table des extrafields par module
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_module_extrafields (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_site_module integer NOT NULL,
  description text,
  INDEX idx_fk_site_module (fk_site_module)
) ENGINE=innodb;

-- Création de la table pour la synthèse CDC
CREATE TABLE IF NOT EXISTS llx_rendez_vous_synthese_cdc (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_projet integer NOT NULL,
  fk_source_cdc integer,
  version varchar(10),
  date_creation datetime,
  date_demo datetime,
  date_livraison datetime,
  nb_rdv_amont varchar(100),
  nb_rdv_aval varchar(100),
  nb_jours_formations varchar(100),
  contexte_projet text,
  objectifs_principaux text,
  perimetre_projet text,
  fonctionnalites_principales text,
  contraintes_techniques text,
  ressources_mobilisees text,
  roles_responsabilites text,
  delais_globaux text,
  jalons_importants text,
  criteres_acceptation text,
  procedure_validation text,
  budget_estimatif text,
  modalites_paiement text,
  INDEX idx_fk_projet (fk_projet)
) ENGINE=innodb;

-- Création de la table pour les démos créées
CREATE TABLE IF NOT EXISTS llx_rendez_vous_demo (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_projet integer NOT NULL,
  demo_name varchar(255) NOT NULL,
  demo_url varchar(500),
  demo_path varchar(500),
  demo_db_name varchar(255),
  modules_selectionnes text,
  date_creation datetime,
  date_expiration datetime,
  statut tinyint DEFAULT 1,
  notes text,
  INDEX idx_fk_projet (fk_projet),
  INDEX idx_demo_name (demo_name),
  INDEX idx_statut (statut)
) ENGINE=innodb;

-- Création de la table des logiciels (constantes)
CREATE TABLE IF NOT EXISTS llx_avimm_constante_logiciel (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  libelle varchar(255) NOT NULL,
  description text,
  active tinyint DEFAULT 1
) ENGINE=innodb;

-- Insertion des logiciels par défaut
INSERT IGNORE INTO llx_avimm_constante_logiciel (libelle, description) VALUES
('Dolibarr', 'ERP/CRM Dolibarr'),
('VTiger', 'CRM VTiger'),
('WordPress', 'CMS WordPress'),
('PrestaShop', 'E-commerce PrestaShop'),
('Magento', 'E-commerce Magento'),
('Autre', 'Autre logiciel');
