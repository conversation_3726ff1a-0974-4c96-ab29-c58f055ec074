<?php
// <PERSON><PERSON><PERSON> générée automatiquement
// Date: 2025-05-27 14:05:21
// Site: ProdRH (ID: 2)
// Projet: 1
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - ProdRH</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .content { padding: 30px; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .info-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }
        .info-card h3 { margin-top: 0; color: #667eea; }
        .modules-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .module-card { background: #fff; border: 1px solid #e0e0e0; border-radius: 8px; padding: 15px; transition: transform 0.2s; }
        .module-card:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .module-card.active { border-color: #28a745; background: #f8fff9; }
        .module-card h4 { margin: 0 0 10px 0; color: #333; }
        .badge { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .badge.active { background: #28a745; color: white; }
        .badge.inactive { background: #6c757d; color: white; }
        .constants-list { margin-top: 10px; }
        .constant-item { background: #e9ecef; padding: 5px 10px; margin: 2px 0; border-radius: 4px; font-size: 0.9em; }
        .constant-item.active { background: #d4edda; color: #155724; }
        .launch-section { background: #e8f4fd; padding: 20px; border-radius: 8px; text-align: center; margin-top: 30px; }
        .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: background 0.3s; }
        .btn:hover { background: #5a6fd8; }
        .btn.secondary { background: #6c757d; }
        .btn.secondary:hover { background: #5a6268; }
        .stats { display: flex; justify-content: space-around; text-align: center; margin: 20px 0; }
        .stat { }
        .stat-number { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Démo Dolibarr</h1>
            <p>Configuration personnalisée pour ProdRH</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📋 Informations du Site</h2>
                <div class="info-grid">
                    <div class="info-card">
                        <h3>Détails du Site</h3>
                        <p><strong>Nom:</strong> ProdRH</p>
                        <p><strong>Type:</strong> Production</p>
                        <p><strong>Description:</strong> </p>
                        <p><strong>Hébergement:</strong> O2switch</p>
                    </div>
                    <div class="info-card">
                        <h3>Configuration</h3>
                        <p><strong>Nombre d'utilisateurs:</strong> 1</p>
                        <p><strong>Logiciel:</strong> Dolibarr</p>
                        <p><strong>Date de création:</strong> 27/05/2025 14:05</p>
                    </div>
                </div>
            </div>
            <div class="stats">
                <div class="stat">
                    <div class="stat-number">0</div>
                    <div class="stat-label">Modules</div>
                </div>
                <div class="stat">
                    <div class="stat-number">0</div>
                    <div class="stat-label">Constantes</div>
                </div>
                <div class="stat">
                    <div class="stat-number">0</div>
                    <div class="stat-label">Extrafields</div>
                </div>
                <div class="stat">
                    <div class="stat-number">0</div>
                    <div class="stat-label">Dév. Spé.</div>
                </div>
            </div>
            <div class="launch-section">
                <h2>🎯 Lancer la Démo</h2>
                <p>Cette configuration est prête à être déployée. Vous pouvez :</p>
                <div style="margin: 20px 0;">
                    <a href="https://demo.dolibarr.org" target="_blank" class="btn">Accéder à la démo en ligne</a>
                    <a href="#" onclick="alert('Fonctionnalité en développement')" class="btn secondary">Créer une instance locale</a>
                </div>
                <p><small>La démo en ligne vous permettra de tester Dolibarr avec une configuration similaire.</small></p>
            </div>
            <div class="section">
                <h2>📞 Support</h2>
                <p>Pour toute question concernant cette démo ou pour obtenir une instance personnalisée, contactez notre équipe.</p>
                <p><strong>Durée de validité:</strong> Cette démo est disponible pendant 30 jours.</p>
            </div>
        </div>
    </div>
</body>
</html>
