<?php
/**
 *	\file       rendezvousclient/test_dolibarr_activation.php
 *	\ingroup    rendezvousclient
 *	\brief      Test de l'activation automatique des modules Dolibarr
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
dol_include_once('/rendezvousclient/site/class/site.class.php');
dol_include_once('/rendezvousclient/site/class/parametragesite.class.php');
dol_include_once('/rendezvousclient/core/class/dolibarr_manager.class.php');

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');
$site_id = GETPOST('site_id', 'int');

/*
 * Actions
 */

if ($action == 'test_activation' && $site_id > 0) {
    try {
        // Charger le site
        $site = new Site($db);
        $result = $site->fetch($site_id);
        
        if ($result <= 0) {
            throw new Exception("Site non trouvé (ID: $site_id)");
        }
        
        // Charger le parametrage du site
        $parametragesite = new ParametrageSite($db);
        $parametragesite->fetch($site->rowid);
        
        // Vérifier la configuration de l'instance cible
        if (empty($conf->global->RENDEZVOUSCLIENT_DEMO_DB_HOST)) {
            throw new Exception("Configuration de l'instance cible manquante. Configurez-la d'abord.");
        }
        
        // Configuration de l'instance cible
        $target_config = array(
            'type' => 'mysqli',
            'host' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_HOST,
            'database' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_NAME,
            'user' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_USER,
            'password' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PASSWORD ?? '',
            'port' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PORT ?? 3306,
            'prefix' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PREFIX ?? 'llx_',
            'url' => $conf->global->RENDEZVOUSCLIENT_DEMO_URL
        );
        
        // Créer le gestionnaire Dolibarr
        $dolibarr_manager = new DolibarrManager($db);
        
        if (!$dolibarr_manager->setTargetInstance($target_config)) {
            throw new Exception("Configuration de l'instance cible invalide: " . $dolibarr_manager->error);
        }
        
        // Préparer la configuration des modules
        $modules_config = array();
        if (!empty($parametragesite->modules)) {
            foreach($parametragesite->modules as $module_id => $module_data){
                if(isset($module_data['checked']) && $module_data['checked'] == 1){
                    $modules_config[$module_id] = $module_data;
                }
            }
        }
        
        // Configurer l'instance Dolibarr
        $result = $dolibarr_manager->configureDolibarrInstance($site, $modules_config);
        
        if ($result['success']) {
            print '<div class="ok">';
            print '<h3>✅ Configuration réussie !</h3>';
            print '<p><strong>Instance configurée :</strong> <a href="'.$target_config['url'].'" target="_blank">'.$target_config['url'].'</a></p>';
            print '<p><strong>Modules activés :</strong> '.count($result['modules_activated']).'</p>';
            print '<p><strong>Constantes ajoutées :</strong> '.count($result['constants_added']).'</p>';
            
            if (!empty($result['modules_activated'])) {
                print '<h4>Modules activés :</h4>';
                print '<ul>';
                foreach ($result['modules_activated'] as $module) {
                    print '<li><strong>'.$module.'</strong></li>';
                }
                print '</ul>';
            }
            
            if (!empty($result['constants_added'])) {
                print '<h4>Constantes ajoutées :</h4>';
                print '<ul>';
                foreach ($result['constants_added'] as $constant) {
                    print '<li><code>'.$constant.'</code></li>';
                }
                print '</ul>';
            }
            
            print '</div>';
            
        } else {
            print '<div class="error">';
            print '<h3>❌ Erreur lors de la configuration</h3>';
            if (!empty($result['errors'])) {
                print '<ul>';
                foreach ($result['errors'] as $error) {
                    print '<li>'.$error.'</li>';
                }
                print '</ul>';
            }
            print '</div>';
        }
        
    } catch (Exception $e) {
        print '<div class="error">';
        print '<h3>❌ Erreur</h3>';
        print '<p>'.$e->getMessage().'</p>';
        print '</div>';
    }
}

if ($action == 'simulate_activation' && $site_id > 0) {
    // Simulation sans modification réelle
    try {
        $site = new Site($db);
        $site->fetch($site_id);
        
        $parametragesite = new ParametrageSite($db);
        $parametragesite->fetch($site->rowid);
        
        print '<div class="info">';
        print '<h3>🔍 Simulation de l\'activation</h3>';
        print '<p><strong>Site :</strong> '.$site->nom.'</p>';
        
        if (!empty($parametragesite->modules)) {
            print '<h4>Modules qui seraient activés :</h4>';
            print '<table class="border centpercent">';
            print '<tr class="liste_titre">';
            print '<th>Module</th>';
            print '<th>Nom Dolibarr</th>';
            print '<th>Constantes</th>';
            print '</tr>';
            
            foreach($parametragesite->modules as $module_id => $module_data){
                if(isset($module_data['checked']) && $module_data['checked'] == 1){
                    print '<tr class="oddeven">';
                    print '<td><strong>'.$module_data['libelle'].'</strong></td>';
                    
                    // Simuler le nom du module Dolibarr
                    $dolibarr_module = 'MAIN_MODULE_' . strtoupper(str_replace(' ', '_', $module_data['libelle']));
                    print '<td><code>'.$dolibarr_module.'</code></td>';
                    
                    // Compter les constantes
                    $nb_constants = 0;
                    if (isset($module_data['constants'])) {
                        foreach ($module_data['constants'] as $const_data) {
                            if (isset($const_data['checked']) && $const_data['checked'] == 1) {
                                $nb_constants++;
                            }
                        }
                    }
                    print '<td>'.$nb_constants.' constante(s)</td>';
                    print '</tr>';
                }
            }
            
            print '</table>';
        } else {
            print '<p><em>Aucun module configuré pour ce site</em></p>';
        }
        
        print '</div>';
        
    } catch (Exception $e) {
        print '<div class="error">Erreur : '.$e->getMessage().'</div>';
    }
}

/*
 * View
 */

$title = 'Test d\'activation automatique Dolibarr';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce test permet de vérifier l\'activation automatique des modules dans une instance Dolibarr cible.';
print '</div>';

// Vérification de la configuration
print '<h3>1. Vérification de la configuration</h3>';

$config_ok = true;
$required_params = array(
    'RENDEZVOUSCLIENT_DEMO_DB_HOST' => 'Hôte de la base de données',
    'RENDEZVOUSCLIENT_DEMO_DB_NAME' => 'Nom de la base de données',
    'RENDEZVOUSCLIENT_DEMO_DB_USER' => 'Utilisateur de la base de données',
    'RENDEZVOUSCLIENT_DEMO_URL' => 'URL de l\'instance Dolibarr'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Paramètre</th>';
print '<th>Statut</th>';
print '</tr>';

foreach ($required_params as $param => $label) {
    $configured = !empty($conf->global->$param);
    if (!$configured) $config_ok = false;
    
    print '<tr class="oddeven">';
    print '<td><strong>'.$label.'</strong></td>';
    print '<td>';
    if ($configured) {
        print '<span class="badge badge-status4 badge-status">✅ OK</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">❌ MANQUANT</span>';
    }
    print '</td>';
    print '</tr>';
}

print '</table>';

if (!$config_ok) {
    print '<div class="warning">';
    print '<strong>⚠️ Configuration incomplète</strong> - ';
    print '<a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/admin/setup_dolibarr_instance.php">Configurez l\'instance cible</a>';
    print '</div>';
}

// Test de connexion
if ($config_ok) {
    print '<br><h3>2. Test de connexion</h3>';
    
    try {
        $target_config = array(
            'type' => 'mysqli',
            'host' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_HOST,
            'database' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_NAME,
            'user' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_USER,
            'password' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PASSWORD ?? '',
            'port' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PORT ?? 3306,
            'prefix' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PREFIX ?? 'llx_',
            'url' => $conf->global->RENDEZVOUSCLIENT_DEMO_URL
        );
        
        $dolibarr_manager = new DolibarrManager($db);
        
        if ($dolibarr_manager->setTargetInstance($target_config)) {
            print '<div class="ok">✅ Connexion à l\'instance cible réussie</div>';
        } else {
            print '<div class="error">❌ Erreur de connexion: ' . $dolibarr_manager->error . '</div>';
            $config_ok = false;
        }
    } catch (Exception $e) {
        print '<div class="error">❌ Exception: ' . $e->getMessage() . '</div>';
        $config_ok = false;
    }
}

// Sélection du site à tester
if ($config_ok) {
    print '<br><h3>3. Sélection du site à tester</h3>';
    
    if (!$site_id) {
        $sql = "SELECT s.rowid, s.nom, soc.nom as nom_societe, p.ref as projet_ref
                FROM ".MAIN_DB_PREFIX."rendez_vous_site s
                LEFT JOIN ".MAIN_DB_PREFIX."projet p ON s.fk_projet = p.rowid
                LEFT JOIN ".MAIN_DB_PREFIX."societe soc ON p.socid = soc.rowid
                ORDER BY s.rowid DESC LIMIT 10";
        
        $resql = $db->query($sql);
        
        if ($resql && $db->num_rows($resql) > 0) {
            print '<table class="border centpercent">';
            print '<tr class="liste_titre">';
            print '<th>ID</th>';
            print '<th>Site</th>';
            print '<th>Société</th>';
            print '<th>Projet</th>';
            print '<th>Actions</th>';
            print '</tr>';
            
            while ($obj = $db->fetch_object($resql)) {
                print '<tr class="oddeven">';
                print '<td>'.$obj->rowid.'</td>';
                print '<td><strong>'.$obj->nom.'</strong></td>';
                print '<td>'.$obj->nom_societe.'</td>';
                print '<td>'.$obj->projet_ref.'</td>';
                print '<td>';
                print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=simulate_activation&site_id='.$obj->rowid.'&token='.newToken().'">Simuler</a> ';
                print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_activation&site_id='.$obj->rowid.'&token='.newToken().'">Activer</a>';
                print '</td>';
                print '</tr>';
            }
            
            print '</table>';
        } else {
            print '<div class="warning">Aucun site trouvé. <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créez un site</a> pour tester.</div>';
        }
    }
} else {
    print '<div class="warning">Configurez d\'abord l\'instance cible pour pouvoir tester l\'activation.</div>';
}

// Instructions
print '<br><h3>Instructions</h3>';

print '<div class="info">';
print '<h4>Comment fonctionne l\'activation automatique :</h4>';
print '<ol>';
print '<li><strong>Récupération de la configuration</strong> - Le système lit les modules et constantes cochés dans le parametrage du site</li>';
print '<li><strong>Connexion à l\'instance cible</strong> - Connexion à la base de données Dolibarr vierge</li>';
print '<li><strong>Activation des modules</strong> - Ajout des constantes MAIN_MODULE_XXX dans la table llx_const</li>';
print '<li><strong>Configuration des constantes</strong> - Ajout des constantes spécifiques cochées</li>';
print '<li><strong>Configuration de la société</strong> - Mise à jour des informations de base</li>';
print '</ol>';
print '</div>';

print '<div class="warning">';
print '<h4>⚠️ Important :</h4>';
print '<ul>';
print '<li>L\'instance cible doit être une installation Dolibarr vierge</li>';
print '<li>Les modules seront activés directement dans la base de données</li>';
print '<li>Les constantes seront ajoutées selon la configuration du site</li>';
print '<li>L\'instance sera accessible immédiatement après configuration</li>';
print '</ul>';
print '</div>';

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/admin/setup_dolibarr_instance.php">Configurer l\'instance cible</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_demo_creation.php">Test création démo</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
