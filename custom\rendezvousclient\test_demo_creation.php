<?php
/**
 *	\file       rendezvousclient/test_demo_creation.php
 *	\ingroup    rendezvousclient
 *	\brief      Test de création de démos <PERSON>
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

dol_include_once('/rendezvousclient/site/class/site.class.php');
dol_include_once('/rendezvousclient/site/class/democreator.class.php');
dol_include_once('/rendezvousclient/site/class/parametragesite.class.php');

$action = GETPOST('action', 'aZ09');
$site_id = GETPOST('site_id', 'int');

/*
 * Actions
 */

if ($action == 'test_demo' && $site_id > 0) {
    try {
        // Charger le site
        $site = new Site($db);
        $result = $site->fetch($site_id);
        
        if ($result <= 0) {
            throw new Exception("Site non trouvé (ID: $site_id)");
        }
        
        // Charger le parametrage du site
        $parametragesite = new ParametrageSite($db);
        $parametragesite->fetch($site->rowid);
        
        // Récupérer les modules sélectionnés
        $modules_selectionnes = array();
        if (!empty($parametragesite->modules)) {
            foreach($parametragesite->modules as $module_id => $module){
                if(isset($module['checked']) && $module['checked'] == 1){
                    $modules_selectionnes[] = $module_id;
                }
            }
        }
        
        // Créer la démo
        $demo_creator = new DemoCreator($db);
        $demo_url = $demo_creator->createDemo($site, $modules_selectionnes);
        
        if($demo_url){
            setEventMessages("Démo créée avec succès ! <a href='$demo_url' target='_blank'>Voir la démo</a>", null, 'mesgs');
            
            // Afficher les détails
            print '<div class="ok">';
            print '<h3>✅ Démo créée avec succès !</h3>';
            print '<p><strong>URL de la démo :</strong> <a href="'.$demo_url.'" target="_blank">'.$demo_url.'</a></p>';
            print '<p><strong>Modules sélectionnés :</strong> '.count($modules_selectionnes).'</p>';
            print '<p><strong>Site :</strong> '.$site->nom.'</p>';
            print '</div>';
            
        } else {
            $error_msg = $demo_creator->error ?: "Erreur inconnue lors de la création de la démo";
            setEventMessages("Erreur lors de la création de la démo: $error_msg", null, 'errors');
        }
        
    } catch (Exception $e) {
        setEventMessages("Erreur : " . $e->getMessage(), null, 'errors');
    }
}

if ($action == 'create_test_data') {
    // Créer des données de test pour les modules et constantes
    $test_results = array();
    
    // Vérifier s'il y a des logiciels
    $sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."avimm_constante_logiciel";
    $resql = $db->query($sql);
    $nb_logiciels = 0;
    if ($resql) {
        $obj = $db->fetch_object($resql);
        $nb_logiciels = $obj->nb;
    }
    
    if ($nb_logiciels == 0) {
        // Créer quelques logiciels de test
        $logiciels_test = array(
            'Dolibarr Standard',
            'Dolibarr Pro',
            'Dolibarr Enterprise',
            'Autre'
        );
        
        foreach ($logiciels_test as $logiciel) {
            $sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_logiciel (libelle) VALUES ('".$db->escape($logiciel)."')";
            if ($db->query($sql)) {
                $test_results[] = array('action' => 'Création logiciel '.$logiciel, 'status' => 'OK');
            }
        }
    }
    
    // Vérifier s'il y a des modules
    $sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."avimm_constante_module";
    $resql = $db->query($sql);
    $nb_modules = 0;
    if ($resql) {
        $obj = $db->fetch_object($resql);
        $nb_modules = $obj->nb;
    }
    
    if ($nb_modules < 5) {
        // Créer quelques modules de test
        $modules_test = array(
            array('libelle' => 'Gestion Commerciale', 'fk_logiciel' => 1),
            array('libelle' => 'Comptabilité', 'fk_logiciel' => 1),
            array('libelle' => 'Gestion de Stock', 'fk_logiciel' => 1),
            array('libelle' => 'CRM', 'fk_logiciel' => 1),
            array('libelle' => 'Projets', 'fk_logiciel' => 1),
            array('libelle' => 'RH', 'fk_logiciel' => 2),
            array('libelle' => 'E-commerce', 'fk_logiciel' => 2)
        );
        
        foreach ($modules_test as $module) {
            $sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_module (libelle, fk_logiciel) VALUES ('".$db->escape($module['libelle'])."', ".$module['fk_logiciel'].")";
            if ($db->query($sql)) {
                $test_results[] = array('action' => 'Création module '.$module['libelle'], 'status' => 'OK');
            }
        }
    }
    
    // Vérifier s'il y a des constantes
    $sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."avimm_constante";
    $resql = $db->query($sql);
    $nb_constantes = 0;
    if ($resql) {
        $obj = $db->fetch_object($resql);
        $nb_constantes = $obj->nb;
    }
    
    if ($nb_constantes < 10) {
        // Créer quelques constantes de test
        $constantes_test = array(
            array('libelle' => 'MAIN_MODULE_SOCIETE', 'description' => 'Module Tiers/Sociétés'),
            array('libelle' => 'MAIN_MODULE_FACTURE', 'description' => 'Module Facturation'),
            array('libelle' => 'MAIN_MODULE_COMMANDE', 'description' => 'Module Commandes'),
            array('libelle' => 'MAIN_MODULE_STOCK', 'description' => 'Module Gestion de Stock'),
            array('libelle' => 'MAIN_MODULE_COMPTABILITE', 'description' => 'Module Comptabilité'),
            array('libelle' => 'MAIN_MODULE_PROJET', 'description' => 'Module Projets'),
            array('libelle' => 'MAIN_MODULE_ADHERENT', 'description' => 'Module Adhérents'),
            array('libelle' => 'MAIN_MODULE_AGENDA', 'description' => 'Module Agenda'),
            array('libelle' => 'MAIN_MODULE_EXPEDITION', 'description' => 'Module Expéditions'),
            array('libelle' => 'MAIN_MODULE_CONTRAT', 'description' => 'Module Contrats')
        );
        
        foreach ($constantes_test as $constante) {
            $sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante (libelle, description) VALUES ('".$db->escape($constante['libelle'])."', '".$db->escape($constante['description'])."')";
            if ($db->query($sql)) {
                $constante_id = $db->last_insert_id(MAIN_DB_PREFIX."avimm_constante");
                $test_results[] = array('action' => 'Création constante '.$constante['libelle'], 'status' => 'OK');
                
                // Lier la constante à un module (exemple : les 3 premières constantes au module 1, etc.)
                $module_id = ceil($constante_id / 3);
                $sql_link = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_inmodule (fk_constante, fk_module) VALUES ($constante_id, $module_id)";
                $db->query($sql_link);
            }
        }
    }
    
    if (!empty($test_results)) {
        setEventMessages(count($test_results)." éléments de test créés", null, 'mesgs');
    }
}

/*
 * View
 */

$title = 'Test de création de démos Dolibarr';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce test permet de créer des démos Dolibarr basées sur les modules et constantes configurés dans le parametrage des sites.';
print '</div>';

// Vérifications
print '<h3>1. Vérifications du système</h3>';

$checks = array();

// Classe DemoCreator
try {
    $demo_creator = new DemoCreator($db);
    $checks[] = array('test' => 'Classe DemoCreator', 'result' => true, 'message' => 'Chargée avec succès');
} catch (Exception $e) {
    $checks[] = array('test' => 'Classe DemoCreator', 'result' => false, 'message' => 'Erreur : ' . $e->getMessage());
}

// Répertoire de démos
$demos_dir = DOL_DATA_ROOT . '/demos/';
$can_create_demos = is_dir($demos_dir) || dol_mkdir($demos_dir);
$checks[] = array(
    'test' => 'Répertoire de démos',
    'result' => $can_create_demos,
    'message' => $can_create_demos ? 'Accessible en écriture' : 'Impossible de créer/accéder'
);

// Tables nécessaires
$tables = array('avimm_constante_module', 'avimm_constante', 'avimm_constante_inmodule', 'rendez_vous_site');
foreach ($tables as $table) {
    $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
    $resql = $db->query($sql);
    $exists = ($resql && $db->num_rows($resql) > 0);
    $checks[] = array(
        'test' => "Table $table",
        'result' => $exists,
        'message' => $exists ? 'Existe' : 'Manquante'
    );
}

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Vérification</th>';
print '<th>Résultat</th>';
print '<th>Message</th>';
print '</tr>';

foreach ($checks as $check) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$check['test'].'</strong></td>';
    print '<td>';
    if ($check['result']) {
        print '<span class="badge badge-status4 badge-status">OK</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">ERREUR</span>';
    }
    print '</td>';
    print '<td>'.$check['message'].'</td>';
    print '</tr>';
}

print '</table>';

// Données disponibles
print '<br><h3>2. Données disponibles</h3>';

$sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."rendez_vous_site";
$resql = $db->query($sql);
$nb_sites = 0;
if ($resql) {
    $obj = $db->fetch_object($resql);
    $nb_sites = $obj->nb;
}

$sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."avimm_constante_module";
$resql = $db->query($sql);
$nb_modules = 0;
if ($resql) {
    $obj = $db->fetch_object($resql);
    $nb_modules = $obj->nb;
}

$sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."avimm_constante";
$resql = $db->query($sql);
$nb_constantes = 0;
if ($resql) {
    $obj = $db->fetch_object($resql);
    $nb_constantes = $obj->nb;
}

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Type de données</th>';
print '<th>Nombre</th>';
print '<th>Statut</th>';
print '</tr>';

$data_types = array(
    'Sites' => $nb_sites,
    'Modules' => $nb_modules,
    'Constantes' => $nb_constantes
);

foreach ($data_types as $type => $count) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$type.'</strong></td>';
    print '<td>'.$count.'</td>';
    print '<td>';
    if ($count > 0) {
        print '<span class="badge badge-status4 badge-status">DISPONIBLE</span>';
    } else {
        print '<span class="badge badge-status7 badge-status">AUCUNE</span>';
    }
    print '</td>';
    print '</tr>';
}

print '</table>';

// Bouton pour créer des données de test
if ($nb_modules < 5 || $nb_constantes < 10) {
    print '<br><div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=create_test_data&token='.newToken().'">Créer des données de test</a>';
    print '</div>';
}

// Test de création de démo
if ($nb_sites > 0) {
    print '<br><h3>3. Test de création de démo</h3>';
    
    if (!$site_id) {
        $sql = "SELECT s.rowid, s.nom, soc.nom as nom_societe 
                FROM ".MAIN_DB_PREFIX."rendez_vous_site s
                LEFT JOIN ".MAIN_DB_PREFIX."projet p ON s.fk_projet = p.rowid
                LEFT JOIN ".MAIN_DB_PREFIX."societe soc ON p.socid = soc.rowid
                ORDER BY s.rowid DESC LIMIT 5";
        
        $resql = $db->query($sql);
        
        if ($resql && $db->num_rows($resql) > 0) {
            print '<table class="border centpercent">';
            print '<tr class="liste_titre">';
            print '<th>ID</th>';
            print '<th>Site</th>';
            print '<th>Société</th>';
            print '<th>Action</th>';
            print '</tr>';
            
            while ($obj = $db->fetch_object($resql)) {
                print '<tr class="oddeven">';
                print '<td>'.$obj->rowid.'</td>';
                print '<td><strong>'.$obj->nom.'</strong></td>';
                print '<td>'.$obj->nom_societe.'</td>';
                print '<td>';
                print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_demo&site_id='.$obj->rowid.'&token='.newToken().'">Créer démo</a>';
                print '</td>';
                print '</tr>';
            }
            
            print '</table>';
        }
    }
} else {
    print '<br><div class="warning">';
    print 'Aucun site trouvé. <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créez un site</a> pour tester la création de démo.';
    print '</div>';
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créer un site</a>';
if ($nb_sites > 0) {
    print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/parametrage.php?id=1">Parametrage site</a>';
}
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
