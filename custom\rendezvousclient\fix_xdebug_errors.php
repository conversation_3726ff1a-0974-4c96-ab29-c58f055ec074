<?php
/**
 *	\file       rendezvousclient/fix_xdebug_errors.php
 *	\ingroup    rendezvousclient
 *	\brief      Correction des erreurs qui déclenchent Xdebug
 */

// Désactiver temporairement l'affichage des erreurs pour éviter les boucles
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'fix_all') {
    $fixes_applied = array();
    
    // 1. Corriger les variables manquantes dans parametrage.php
    $parametrage_file = DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/site/parametrage.php';
    if (file_exists($parametrage_file)) {
        $content = file_get_contents($parametrage_file);
        $original_content = $content;
        
        // Vérifier si les variables sont déjà définies
        if (strpos($content, '$help_url = ') === false) {
            // Ajouter les variables après la ligne $action = GETPOST
            $content = preg_replace(
                '/(\$action = GETPOST\([^;]+;)/',
                '$1' . "\n" . '$help_url = \'\';' . "\n" . '$contactid = 0; // ID du contact (0 = pas de contact spécifique)',
                $content
            );
            
            if ($content !== $original_content) {
                file_put_contents($parametrage_file, $content);
                $fixes_applied[] = 'Variables $help_url et $contactid ajoutées dans parametrage.php';
            }
        }
    }
    
    // 2. Corriger les erreurs dans la classe DemoCreator
    $democreator_file = DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/site/class/democreator.class.php';
    if (file_exists($democreator_file)) {
        $content = file_get_contents($democreator_file);
        $original_content = $content;
        
        // Corriger la méthode saveDemoInfo si elle n'existe pas
        if (strpos($content, 'function saveDemoInfo') === false) {
            $save_demo_method = '
    /**
     * Save demo information to database
     *
     * @param int $fk_projet Project ID
     * @param string $demo_name Demo name
     * @param string $demo_url Demo URL
     * @param array $modules_selectionnes Selected modules
     * @return bool True if successful
     */
    private function saveDemoInfo($fk_projet, $demo_name, $demo_url, $modules_selectionnes)
    {
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."rendez_vous_demo (fk_projet, nom, url, modules_selectionnes, date_creation)
                VALUES (".(int)$fk_projet.", \'".$this->db->escape($demo_name)."\', \'".$this->db->escape($demo_url)."\', \'".$this->db->escape(implode(\',\', $modules_selectionnes))."\', NOW())";
        
        $resql = $this->db->query($sql);
        if (!$resql) {
            $this->error = "Erreur lors de l\'enregistrement de la démo: " . $this->db->lasterror();
            return false;
        }
        
        return true;
    }';
            
            // Ajouter la méthode avant la dernière accolade
            $content = preg_replace('/(\s*}\s*)$/', $save_demo_method . "\n" . '$1', $content);
            
            if ($content !== $original_content) {
                file_put_contents($democreator_file, $content);
                $fixes_applied[] = 'Méthode saveDemoInfo ajoutée dans DemoCreator';
            }
        }
    }
    
    // 3. Vérifier et créer la table rendez_vous_demo si nécessaire
    $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX."rendez_vous_demo'";
    $resql = $db->query($sql);
    
    if (!$resql || $db->num_rows($resql) == 0) {
        // Créer la table
        $sql = "CREATE TABLE ".MAIN_DB_PREFIX."rendez_vous_demo (
            rowid int(11) NOT NULL AUTO_INCREMENT,
            fk_projet int(11) DEFAULT NULL,
            nom varchar(255) NOT NULL,
            url varchar(500) DEFAULT NULL,
            modules_selectionnes text,
            date_creation datetime DEFAULT NULL,
            PRIMARY KEY (rowid),
            KEY fk_projet (fk_projet)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8";
        
        if ($db->query($sql)) {
            $fixes_applied[] = 'Table rendez_vous_demo créée';
        } else {
            $fixes_applied[] = 'ERREUR: Impossible de créer la table rendez_vous_demo';
        }
    }
    
    // 4. Corriger les erreurs dans document_generator.class.php
    $docgen_file = DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/core/class/document_generator.class.php';
    if (file_exists($docgen_file)) {
        $content = file_get_contents($docgen_file);
        $original_content = $content;
        
        // Corriger les variables globales manquantes
        $content = str_replace(
            'global $conf, $user;',
            'global $conf, $user, $langs;',
            $content
        );
        
        // Corriger l'accès à la méthode getLibLogiciel
        if (strpos($content, '$site->getLibLogiciel') !== false) {
            $content = str_replace(
                '$site->getLibLogiciel($site->fk_logiciel)',
                '($site->fk_logiciel ? "Logiciel ID: " . $site->fk_logiciel : "Non défini")',
                $content
            );
        }
        
        if ($content !== $original_content) {
            file_put_contents($docgen_file, $content);
            $fixes_applied[] = 'Corrections appliquées dans DocumentGenerator';
        }
    }
    
    // 5. Créer les répertoires manquants
    $directories = array(
        DOL_DATA_ROOT . '/rendezvousclient/',
        DOL_DATA_ROOT . '/rendezvousclient/sites/',
        DOL_DATA_ROOT . '/demos/',
        DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/core/templates/'
    );
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (dol_mkdir($dir)) {
                $fixes_applied[] = 'Répertoire créé: ' . $dir;
            } else {
                $fixes_applied[] = 'ERREUR: Impossible de créer ' . $dir;
            }
        }
    }
    
    // 6. Corriger les permissions de fichiers
    $files_to_fix = array(
        DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/site/parametrage.php',
        DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/site/card.php',
        DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/demo/manage.php'
    );
    
    foreach ($files_to_fix as $file) {
        if (file_exists($file) && !is_readable($file)) {
            if (chmod($file, 0644)) {
                $fixes_applied[] = 'Permissions corrigées pour: ' . basename($file);
            }
        }
    }
    
    if (!empty($fixes_applied)) {
        setEventMessages(count($fixes_applied) . " correction(s) appliquée(s)", null, 'mesgs');
    } else {
        setEventMessages("Aucune correction nécessaire", null, 'mesgs');
    }
}

if ($action == 'disable_xdebug') {
    // Créer un fichier .htaccess pour désactiver Xdebug dans le module
    $htaccess_content = "# Désactivation temporaire de Xdebug pour le module rendezvousclient\n";
    $htaccess_content .= "php_flag xdebug.default_enable off\n";
    $htaccess_content .= "php_flag xdebug.profiler_enable off\n";
    $htaccess_content .= "php_flag xdebug.auto_trace off\n";
    
    $htaccess_file = DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/.htaccess';
    
    if (file_put_contents($htaccess_file, $htaccess_content)) {
        setEventMessages("Xdebug désactivé pour le module (fichier .htaccess créé)", null, 'mesgs');
    } else {
        setEventMessages("Impossible de créer le fichier .htaccess", null, 'errors');
    }
}

if ($action == 'enable_xdebug') {
    // Supprimer le fichier .htaccess
    $htaccess_file = DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/.htaccess';
    
    if (file_exists($htaccess_file)) {
        if (unlink($htaccess_file)) {
            setEventMessages("Xdebug réactivé (fichier .htaccess supprimé)", null, 'mesgs');
        } else {
            setEventMessages("Impossible de supprimer le fichier .htaccess", null, 'errors');
        }
    } else {
        setEventMessages("Xdebug était déjà activé", null, 'mesgs');
    }
}

/*
 * View
 */

$title = 'Correction des erreurs Xdebug';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script corrige automatiquement les erreurs qui déclenchent les messages Xdebug dans le module.';
print '</div>';

// Diagnostic rapide
print '<h3>🔍 Diagnostic rapide</h3>';

$diagnostics = array();

// Vérifier Xdebug
$xdebug_enabled = function_exists('xdebug_info');
$diagnostics[] = array(
    'test' => 'Xdebug activé',
    'status' => $xdebug_enabled ? 'OUI' : 'NON',
    'severity' => $xdebug_enabled ? 'warning' : 'ok'
);

// Vérifier les fichiers critiques
$critical_files = array(
    'site/parametrage.php',
    'site/class/democreator.class.php',
    'core/class/document_generator.class.php'
);

foreach ($critical_files as $file) {
    $full_path = DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/' . $file;
    $exists = file_exists($full_path);
    $diagnostics[] = array(
        'test' => $file,
        'status' => $exists ? 'EXISTE' : 'MANQUANT',
        'severity' => $exists ? 'ok' : 'error'
    );
}

// Vérifier la table demo
$sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX."rendez_vous_demo'";
$resql = $db->query($sql);
$table_exists = ($resql && $db->num_rows($resql) > 0);
$diagnostics[] = array(
    'test' => 'Table rendez_vous_demo',
    'status' => $table_exists ? 'EXISTE' : 'MANQUANTE',
    'severity' => $table_exists ? 'ok' : 'error'
);

// Vérifier les répertoires
$directories = array(
    DOL_DATA_ROOT . '/demos/',
    DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/core/templates/'
);

foreach ($directories as $dir) {
    $exists = is_dir($dir);
    $diagnostics[] = array(
        'test' => 'Répertoire ' . basename($dir),
        'status' => $exists ? 'EXISTE' : 'MANQUANT',
        'severity' => $exists ? 'ok' : 'warning'
    );
}

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Élément</th>';
print '<th>Statut</th>';
print '</tr>';

$nb_errors = 0;
$nb_warnings = 0;

foreach ($diagnostics as $diag) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$diag['test'].'</strong></td>';
    print '<td>';
    
    switch ($diag['severity']) {
        case 'ok':
            print '<span class="badge badge-status4 badge-status">'.$diag['status'].'</span>';
            break;
        case 'warning':
            print '<span class="badge badge-status7 badge-status">'.$diag['status'].'</span>';
            $nb_warnings++;
            break;
        case 'error':
            print '<span class="badge badge-status8 badge-status">'.$diag['status'].'</span>';
            $nb_errors++;
            break;
    }
    
    print '</td>';
    print '</tr>';
}

print '</table>';

// Actions de correction
print '<br><h3>🛠️ Actions de correction</h3>';

if ($nb_errors > 0 || $nb_warnings > 0) {
    print '<div class="warning">';
    print '<strong>⚠️ '.$nb_errors.' erreur(s) et '.$nb_warnings.' avertissement(s) détecté(s)</strong>';
    print '</div>';
    
    print '<div class="tabsAction">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=fix_all&token='.newToken().'">Corriger automatiquement</a>';
    print '</div>';
} else {
    print '<div class="ok">';
    print '<strong>✅ Aucun problème détecté</strong>';
    print '</div>';
}

// Gestion de Xdebug
print '<br><h3>🐛 Gestion de Xdebug</h3>';

$htaccess_file = DOL_DOCUMENT_ROOT . '/custom/rendezvousclient/.htaccess';
$xdebug_disabled = file_exists($htaccess_file);

if ($xdebug_enabled) {
    print '<div class="warning">';
    print '<strong>Xdebug est activé</strong> - Cela peut causer des erreurs dans le module.';
    print '</div>';
    
    print '<div class="tabsAction">';
    if (!$xdebug_disabled) {
        print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=disable_xdebug&token='.newToken().'">Désactiver Xdebug pour ce module</a>';
    } else {
        print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=enable_xdebug&token='.newToken().'">Réactiver Xdebug</a>';
        print '<div class="info">Xdebug est actuellement désactivé pour ce module</div>';
    }
    print '</div>';
} else {
    print '<div class="ok">';
    print '<strong>Xdebug n\'est pas activé</strong> - Pas de risque d\'erreurs Xdebug.';
    print '</div>';
}

// Affichage des corrections appliquées
if (!empty($fixes_applied)) {
    print '<br><h3>✅ Corrections appliquées</h3>';
    
    print '<ul>';
    foreach ($fixes_applied as $fix) {
        if (strpos($fix, 'ERREUR') === 0) {
            print '<li class="error">'.$fix.'</li>';
        } else {
            print '<li class="ok">'.$fix.'</li>';
        }
    }
    print '</ul>';
}

// Instructions manuelles
print '<br><h3>📋 Instructions manuelles</h3>';

print '<div class="info">';
print '<h4>Si l\'erreur Xdebug persiste :</h4>';
print '<ol>';
print '<li><strong>Vérifiez les logs PHP</strong> de votre serveur web pour identifier l\'erreur exacte</li>';
print '<li><strong>Testez chaque page</strong> individuellement pour isoler le problème</li>';
print '<li><strong>Utilisez les scripts de test</strong> disponibles dans le module</li>';
print '<li><strong>Désactivez temporairement Xdebug</strong> si nécessaire</li>';
print '</ol>';
print '</div>';

// Liens vers les autres outils
print '<br><h3>🔗 Autres outils de débogage</h3>';

print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/debug_errors.php">Debug complet</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_parametrage.php">Test parametrage</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_demo_creation.php">Test démos</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/fix_all_missing_variables.php">Corriger variables</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
