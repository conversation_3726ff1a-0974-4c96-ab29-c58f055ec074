<?php
/**
 *	\file       rendezvousclient/admin/templates.php
 *	\ingroup    rendezvousclient
 *	\brief      Gestion des templates de documents
 */

require_once '../../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';
dol_include_once('/rendezvousclient/core/class/document_generator.class.php');

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');
$help_url = '';

// Créer le répertoire des templates s'il n'existe pas
$templates_dir = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/';
if (!is_dir($templates_dir)) {
    dol_mkdir($templates_dir);
}

/*
 * Actions
 */

if ($action == 'upload_template') {
    if (!empty($_FILES['template_file']['tmp_name'])) {
        $upload_file = $_FILES['template_file'];
        $template_type = GETPOST('template_type', 'alpha');
        
        // Vérifier l'extension
        $file_extension = pathinfo($upload_file['name'], PATHINFO_EXTENSION);
        if (strtolower($file_extension) !== 'odt') {
            setEventMessages("Seuls les fichiers ODT sont acceptés", null, 'errors');
        } else {
            // Nom du fichier de destination
            $template_names = array(
                'cahier_charges' => 'cahier_des_charges_template.odt',
                'proposition_technique' => 'proposition_technique_template.odt',
                'devis' => 'devis_template.odt',
                'synthese_cdc' => 'synthese_cdc_template.odt'
            );
            
            if (isset($template_names[$template_type])) {
                $destination = $templates_dir . $template_names[$template_type];
                
                if (move_uploaded_file($upload_file['tmp_name'], $destination)) {
                    setEventMessages("Template uploadé avec succès", null, 'mesgs');
                } else {
                    setEventMessages("Erreur lors de l'upload du template", null, 'errors');
                }
            } else {
                setEventMessages("Type de template invalide", null, 'errors');
            }
        }
    }
}

if ($action == 'create_default_template') {
    $template_type = GETPOST('template_type', 'alpha');
    
    if (createDefaultTemplate($template_type, $templates_dir)) {
        setEventMessages("Template par défaut créé avec succès", null, 'mesgs');
    } else {
        setEventMessages("Erreur lors de la création du template par défaut", null, 'errors');
    }
}

/**
 * Crée un template par défaut
 */
function createDefaultTemplate($type, $templates_dir)
{
    $template_names = array(
        'cahier_charges' => 'cahier_des_charges_template.odt',
        'proposition_technique' => 'proposition_technique_template.odt',
        'devis' => 'devis_template.odt',
        'synthese_cdc' => 'synthese_cdc_template.odt'
    );
    
    if (!isset($template_names[$type])) {
        return false;
    }
    
    $filename = $templates_dir . $template_names[$type];
    
    // Contenu XML basique pour un document ODT
    $content_xml = '<?xml version="1.0" encoding="UTF-8"?>
<office:document-content xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0">
<office:body>
<office:text>
<text:h text:style-name="Heading_20_1" text:outline-level="1">'.getTemplateTitle($type).'</text:h>
<text:p text:style-name="Standard"/>
<text:p text:style-name="Standard">Date de génération : __DATE_GENERATION__</text:p>
<text:p text:style-name="Standard">Généré par : __UTILISATEUR__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Client</text:h>
<text:p text:style-name="Standard">Société : __SOCIETE_NOM__</text:p>
<text:p text:style-name="Standard">Adresse : __SOCIETE_ADRESSE__</text:p>
<text:p text:style-name="Standard">__SOCIETE_CP__ __SOCIETE_VILLE__</text:p>
<text:p text:style-name="Standard">Téléphone : __SOCIETE_TEL__</text:p>
<text:p text:style-name="Standard">Email : __SOCIETE_EMAIL__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Projet</text:h>
<text:p text:style-name="Standard">Référence : __PROJET_REF__</text:p>
<text:p text:style-name="Standard">Titre : __PROJET_TITRE__</text:p>
<text:p text:style-name="Standard">Description : __PROJET_DESCRIPTION__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Site</text:h>
<text:p text:style-name="Standard">Nom du site : __SITE_NOM__</text:p>
<text:p text:style-name="Standard">Type : __SITE_TYPE__</text:p>
<text:p text:style-name="Standard">Description : __SITE_DESCRIPTION__</text:p>
<text:p text:style-name="Standard">Nombre d\'utilisateurs : __SITE_NB_UTILISATEURS__</text:p>
<text:p text:style-name="Standard">Logiciel : __SITE_LOGICIEL__</text:p>
<text:p text:style-name="Standard">Hébergement : __SITE_HEBERGEMENT__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Utilisateurs</text:h>
<text:p text:style-name="Standard">__SITE_UTILISATEURS__</text:p>

</office:text>
</office:body>
</office:document-content>';
    
    // Créer un fichier ZIP (ODT)
    $zip = new ZipArchive();
    if ($zip->open($filename, ZipArchive::CREATE) !== TRUE) {
        return false;
    }
    
    // Ajouter les fichiers nécessaires
    $zip->addFromString('mimetype', 'application/vnd.oasis.opendocument.text');
    $zip->addFromString('content.xml', $content_xml);
    
    // META-INF/manifest.xml
    $manifest = '<?xml version="1.0" encoding="UTF-8"?>
<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0">
<manifest:file-entry manifest:full-path="/" manifest:media-type="application/vnd.oasis.opendocument.text"/>
<manifest:file-entry manifest:full-path="content.xml" manifest:media-type="text/xml"/>
</manifest:manifest>';
    
    $zip->addFromString('META-INF/manifest.xml', $manifest);
    
    $zip->close();
    
    return true;
}

function getTemplateTitle($type)
{
    $titles = array(
        'cahier_charges' => 'Cahier des Charges',
        'proposition_technique' => 'Proposition Technique et Commerciale',
        'devis' => 'Devis',
        'synthese_cdc' => 'Synthèse du Cahier des Charges'
    );
    
    return isset($titles[$type]) ? $titles[$type] : 'Document';
}

/*
 * View
 */

$title = 'Gestion des templates de documents';
llxHeader('', $title, $help_url);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Gérez ici les templates de documents ODT qui seront utilisés pour générer les documents à partir des informations saisies.';
print '</div>';

// Onglets
$head = array();
$head[0][0] = DOL_URL_ROOT.'/custom/rendezvousclient/admin/templates.php';
$head[0][1] = 'Templates';
$head[0][2] = 'templates';

print dol_get_fiche_head($head, 'templates', '', -1);

// Liste des templates disponibles
print '<h3>Templates disponibles</h3>';

$template_types = array(
    'cahier_charges' => array(
        'name' => 'Cahier des charges',
        'file' => 'cahier_des_charges_template.odt',
        'description' => 'Template pour générer un cahier des charges complet'
    ),
    'proposition_technique' => array(
        'name' => 'Proposition technique',
        'file' => 'proposition_technique_template.odt',
        'description' => 'Template pour générer une proposition technique et commerciale'
    ),
    'devis' => array(
        'name' => 'Devis',
        'file' => 'devis_template.odt',
        'description' => 'Template pour générer un devis'
    ),
    'synthese_cdc' => array(
        'name' => 'Synthèse CDC',
        'file' => 'synthese_cdc_template.odt',
        'description' => 'Template pour générer une synthèse du cahier des charges'
    )
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Type de document</th>';
print '<th>Fichier template</th>';
print '<th>Statut</th>';
print '<th>Actions</th>';
print '</tr>';

foreach ($template_types as $type => $info) {
    $template_path = $templates_dir . $info['file'];
    $exists = file_exists($template_path);
    
    print '<tr class="oddeven">';
    print '<td><strong>'.$info['name'].'</strong><br><em>'.$info['description'].'</em></td>';
    print '<td>'.$info['file'].'</td>';
    print '<td>';
    if ($exists) {
        print '<span class="badge badge-status4 badge-status">Disponible</span>';
        print '<br><small>Modifié le : '.date('d/m/Y H:i', filemtime($template_path)).'</small>';
    } else {
        print '<span class="badge badge-status8 badge-status">Manquant</span>';
    }
    print '</td>';
    print '<td>';
    if ($exists) {
        print '<a href="'.$template_path.'" class="button" target="_blank">Télécharger</a> ';
    }
    print '<a href="'.$_SERVER['PHP_SELF'].'?action=create_default_template&template_type='.$type.'&token='.newToken().'" class="button">Créer défaut</a>';
    print '</td>';
    print '</tr>';
}

print '</table>';

// Formulaire d'upload
print '<br><h3>Uploader un nouveau template</h3>';

print '<form enctype="multipart/form-data" method="post" action="'.$_SERVER['PHP_SELF'].'">';
print '<input type="hidden" name="action" value="upload_template">';
print '<input type="hidden" name="token" value="'.newToken().'">';

print '<table class="border centpercent">';
print '<tr>';
print '<td class="titlefieldcreate">Type de document</td>';
print '<td>';
$options = array();
foreach ($template_types as $type => $info) {
    $options[$type] = $info['name'];
}
print $form->selectarray('template_type', $options, '', 1);
print '</td>';
print '</tr>';

print '<tr>';
print '<td class="titlefieldcreate">Fichier ODT</td>';
print '<td>';
print '<input type="file" name="template_file" accept=".odt" required>';
print '</td>';
print '</tr>';

print '</table>';

print '<div class="center">';
print '<input type="submit" class="button" value="Uploader le template">';
print '</div>';

print '</form>';

// Variables disponibles
print '<br><h3>Variables disponibles dans les templates</h3>';
print '<div class="info">';
print 'Vous pouvez utiliser ces variables dans vos templates ODT. Elles seront automatiquement remplacées lors de la génération :';
print '</div>';

$variables = array(
    'Général' => array(
        '__DATE_GENERATION__' => 'Date de génération (format court)',
        '__DATE_GENERATION_COMPLETE__' => 'Date de génération (format complet)',
        '__UTILISATEUR__' => 'Nom de l\'utilisateur qui génère le document'
    ),
    'Client' => array(
        '__SOCIETE_NOM__' => 'Nom de la société',
        '__SOCIETE_ADRESSE__' => 'Adresse de la société',
        '__SOCIETE_CP__' => 'Code postal',
        '__SOCIETE_VILLE__' => 'Ville',
        '__SOCIETE_TEL__' => 'Téléphone',
        '__SOCIETE_EMAIL__' => 'Email'
    ),
    'Projet' => array(
        '__PROJET_REF__' => 'Référence du projet',
        '__PROJET_TITRE__' => 'Titre du projet',
        '__PROJET_DESCRIPTION__' => 'Description du projet'
    ),
    'Site' => array(
        '__SITE_NOM__' => 'Nom du site',
        '__SITE_TYPE__' => 'Type de site',
        '__SITE_DESCRIPTION__' => 'Description du site',
        '__SITE_NB_UTILISATEURS__' => 'Nombre d\'utilisateurs',
        '__SITE_LOGICIEL__' => 'Logiciel utilisé',
        '__SITE_HEBERGEMENT__' => 'Type d\'hébergement',
        '__SITE_UTILISATEURS__' => 'Liste des utilisateurs avec descriptions'
    ),
    'Entreprise' => array(
        '__ENTREPRISE_NOM__' => 'Nom de votre entreprise',
        '__ENTREPRISE_ADRESSE__' => 'Adresse de votre entreprise',
        '__ENTREPRISE_TEL__' => 'Téléphone de votre entreprise',
        '__ENTREPRISE_EMAIL__' => 'Email de votre entreprise'
    )
);

foreach ($variables as $category => $vars) {
    print '<h4>'.$category.'</h4>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th style="width: 30%">Variable</th>';
    print '<th>Description</th>';
    print '</tr>';
    
    foreach ($vars as $var => $desc) {
        print '<tr class="oddeven">';
        print '<td><code>'.$var.'</code></td>';
        print '<td>'.$desc.'</td>';
        print '</tr>';
    }
    
    print '</table>';
}

print dol_get_fiche_end();

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
