<?php
/**
 *	\file       rendezvousclient/diagnostic_donnees.php
 *	\ingroup    rendezvousclient
 *	\brief      Diagnostic des données manquantes dans la génération
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

dol_include_once('/rendezvousclient/site/class/site.class.php');

$action = GETPOST('action', 'aZ09');
$site_id = GETPOST('site_id', 'int');

/*
 * View
 */

$title = 'Diagnostic des données pour la génération';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce diagnostic identifie toutes les données disponibles et manquantes pour la génération de documents.';
print '</div>';

// 1. Vérifier les tables disponibles
print '<h3>1. Tables disponibles</h3>';

$tables_to_check = array(
    'rendez_vous_site' => 'Sites',
    'rendez_vous_site_utilisateur' => 'Utilisateurs des sites',
    'rendez_vous_site_module' => 'Modules des sites',
    'rendez_vous_site_module_devspe' => 'Développements spécifiques',
    'rendez_vous_site_module_param' => 'Paramètres des modules',
    'rendez_vous_site_module_extrafields' => 'Champs supplémentaires',
    'avimm_constante_logiciel' => 'Logiciels disponibles',
    'avimm_constante_module' => 'Modules disponibles',
    'avimm_constante' => 'Constantes disponibles'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Table</th>';
print '<th>Description</th>';
print '<th>Existe</th>';
print '<th>Nombre d\'enregistrements</th>';
print '</tr>';

foreach ($tables_to_check as $table => $description) {
    $full_table = MAIN_DB_PREFIX . $table;
    
    // Vérifier si la table existe
    $sql = "SHOW TABLES LIKE '".$full_table."'";
    $resql = $db->query($sql);
    $exists = ($resql && $db->num_rows($resql) > 0);
    
    $count = 0;
    if ($exists) {
        $sql = "SELECT COUNT(*) as nb FROM ".$full_table;
        $resql = $db->query($sql);
        if ($resql) {
            $obj = $db->fetch_object($resql);
            $count = $obj->nb;
        }
    }
    
    print '<tr class="oddeven">';
    print '<td><strong>'.$table.'</strong></td>';
    print '<td>'.$description.'</td>';
    print '<td>';
    if ($exists) {
        print '<span class="badge badge-status4 badge-status">OUI</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">NON</span>';
    }
    print '</td>';
    print '<td>'.$count.'</td>';
    print '</tr>';
}

print '</table>';

// 2. Analyser un site spécifique si fourni
if ($site_id > 0) {
    print '<br><h3>2. Analyse détaillée du site ID: '.$site_id.'</h3>';
    
    $site = new Site($db);
    $result = $site->fetch($site_id);
    
    if ($result > 0) {
        print '<h4>Informations de base du site</h4>';
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Propriété</th>';
        print '<th>Valeur</th>';
        print '</tr>';
        
        $site_properties = array(
            'rowid' => 'ID',
            'nom' => 'Nom',
            'type' => 'Type',
            'description' => 'Description',
            'nombre_utilisateur' => 'Nombre d\'utilisateurs',
            'fk_logiciel' => 'ID Logiciel',
            'autre' => 'Autre',
            'hebergement' => 'Hébergement',
            'fk_projet' => 'ID Projet',
            'socid' => 'ID Société'
        );
        
        foreach ($site_properties as $prop => $label) {
            print '<tr class="oddeven">';
            print '<td><strong>'.$label.'</strong></td>';
            print '<td>'.($site->$prop ?: '<em>Vide</em>').'</td>';
            print '</tr>';
        }
        
        print '</table>';
        
        // Utilisateurs du site
        print '<h4>Utilisateurs du site</h4>';
        $site->fetch_lines_utilisateur();
        
        if (!empty($site->lines_utilisateur)) {
            print '<table class="border centpercent">';
            print '<tr class="liste_titre">';
            print '<th>Type</th>';
            print '<th>Description</th>';
            print '</tr>';
            
            foreach ($site->lines_utilisateur as $user_line) {
                print '<tr class="oddeven">';
                print '<td>'.$user_line->type.'</td>';
                print '<td>'.$user_line->descriptif.'</td>';
                print '</tr>';
            }
            
            print '</table>';
        } else {
            print '<div class="warning">Aucun utilisateur défini pour ce site</div>';
        }
        
        // Modules du site
        print '<h4>Modules et paramètres du site</h4>';
        
        $sql = "SELECT sm.rowid, sm.fk_module, sm.checked, cm.libelle as module_name
                FROM ".MAIN_DB_PREFIX."rendez_vous_site_module sm
                LEFT JOIN ".MAIN_DB_PREFIX."avimm_constante_module cm ON sm.fk_module = cm.rowid
                WHERE sm.fk_site = ".$site_id;
        
        $resql = $db->query($sql);
        
        if ($resql && $db->num_rows($resql) > 0) {
            print '<table class="border centpercent">';
            print '<tr class="liste_titre">';
            print '<th>Module</th>';
            print '<th>Activé</th>';
            print '<th>Dev. spé.</th>';
            print '<th>Paramètres</th>';
            print '<th>Extrafields</th>';
            print '</tr>';
            
            while ($obj = $db->fetch_object($resql)) {
                // Compter les dev spé
                $sql_devspe = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."rendez_vous_site_module_devspe WHERE fk_site_module = ".$obj->rowid;
                $resql_devspe = $db->query($sql_devspe);
                $nb_devspe = 0;
                if ($resql_devspe) {
                    $obj_devspe = $db->fetch_object($resql_devspe);
                    $nb_devspe = $obj_devspe->nb;
                }
                
                // Compter les paramètres
                $sql_param = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."rendez_vous_site_module_param WHERE fk_site_module = ".$obj->rowid;
                $resql_param = $db->query($sql_param);
                $nb_param = 0;
                if ($resql_param) {
                    $obj_param = $db->fetch_object($resql_param);
                    $nb_param = $obj_param->nb;
                }
                
                // Compter les extrafields
                $sql_extra = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."rendez_vous_site_module_extrafields WHERE fk_site_module = ".$obj->rowid;
                $resql_extra = $db->query($sql_extra);
                $nb_extra = 0;
                if ($resql_extra) {
                    $obj_extra = $db->fetch_object($resql_extra);
                    $nb_extra = $obj_extra->nb;
                }
                
                print '<tr class="oddeven">';
                print '<td><strong>'.$obj->module_name.'</strong> (ID: '.$obj->fk_module.')</td>';
                print '<td>';
                if ($obj->checked) {
                    print '<span class="badge badge-status4 badge-status">OUI</span>';
                } else {
                    print '<span class="badge badge-status8 badge-status">NON</span>';
                }
                print '</td>';
                print '<td>'.$nb_devspe.'</td>';
                print '<td>'.$nb_param.'</td>';
                print '<td>'.$nb_extra.'</td>';
                print '</tr>';
            }
            
            print '</table>';
        } else {
            print '<div class="warning">Aucun module configuré pour ce site</div>';
        }
        
    } else {
        print '<div class="error">Site non trouvé</div>';
    }
}

// 3. Lister tous les sites pour sélection
if (!$site_id) {
    print '<br><h3>2. Sélectionner un site pour analyse détaillée</h3>';
    
    $sql = "SELECT s.rowid, s.nom, soc.nom as nom_societe, p.ref as projet_ref
            FROM ".MAIN_DB_PREFIX."rendez_vous_site s
            LEFT JOIN ".MAIN_DB_PREFIX."projet p ON s.fk_projet = p.rowid
            LEFT JOIN ".MAIN_DB_PREFIX."societe soc ON p.socid = soc.rowid
            ORDER BY s.rowid DESC LIMIT 10";
    
    $resql = $db->query($sql);
    
    if ($resql && $db->num_rows($resql) > 0) {
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>ID</th>';
        print '<th>Site</th>';
        print '<th>Projet</th>';
        print '<th>Société</th>';
        print '<th>Action</th>';
        print '</tr>';
        
        while ($obj = $db->fetch_object($resql)) {
            print '<tr class="oddeven">';
            print '<td>'.$obj->rowid.'</td>';
            print '<td><strong>'.$obj->nom.'</strong></td>';
            print '<td>'.$obj->projet_ref.'</td>';
            print '<td>'.$obj->nom_societe.'</td>';
            print '<td>';
            print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?site_id='.$obj->rowid.'">Analyser</a>';
            print '</td>';
            print '</tr>';
        }
        
        print '</table>';
    } else {
        print '<div class="warning">Aucun site trouvé</div>';
    }
}

// 4. Constantes et modules disponibles
print '<br><h3>3. Constantes et modules disponibles</h3>';

// Logiciels
$sql = "SELECT rowid, libelle FROM ".MAIN_DB_PREFIX."avimm_constante_logiciel ORDER BY libelle";
$resql = $db->query($sql);

if ($resql && $db->num_rows($resql) > 0) {
    print '<h4>Logiciels disponibles</h4>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>ID</th>';
    print '<th>Libellé</th>';
    print '</tr>';
    
    while ($obj = $db->fetch_object($resql)) {
        print '<tr class="oddeven">';
        print '<td>'.$obj->rowid.'</td>';
        print '<td>'.$obj->libelle.'</td>';
        print '</tr>';
    }
    
    print '</table>';
} else {
    print '<div class="warning">Aucun logiciel trouvé dans avimm_constante_logiciel</div>';
}

// Modules
$sql = "SELECT rowid, libelle FROM ".MAIN_DB_PREFIX."avimm_constante_module ORDER BY libelle";
$resql = $db->query($sql);

if ($resql && $db->num_rows($resql) > 0) {
    print '<br><h4>Modules disponibles</h4>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>ID</th>';
    print '<th>Libellé</th>';
    print '</tr>';
    
    $count = 0;
    while ($obj = $db->fetch_object($resql)) {
        print '<tr class="oddeven">';
        print '<td>'.$obj->rowid.'</td>';
        print '<td>'.$obj->libelle.'</td>';
        print '</tr>';
        $count++;
        if ($count >= 20) {
            print '<tr><td colspan="2"><em>... et '.($db->num_rows($resql) - 20).' autres</em></td></tr>';
            break;
        }
    }
    
    print '</table>';
} else {
    print '<div class="warning">Aucun module trouvé dans avimm_constante_module</div>';
}

// Constantes
$sql = "SELECT rowid, libelle FROM ".MAIN_DB_PREFIX."avimm_constante ORDER BY libelle";
$resql = $db->query($sql);

if ($resql && $db->num_rows($resql) > 0) {
    print '<br><h4>Constantes disponibles</h4>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>ID</th>';
    print '<th>Libellé</th>';
    print '</tr>';
    
    $count = 0;
    while ($obj = $db->fetch_object($resql)) {
        print '<tr class="oddeven">';
        print '<td>'.$obj->rowid.'</td>';
        print '<td>'.$obj->libelle.'</td>';
        print '</tr>';
        $count++;
        if ($count >= 20) {
            print '<tr><td colspan="2"><em>... et '.($db->num_rows($resql) - 20).' autres</em></td></tr>';
            break;
        }
    }
    
    print '</table>';
} else {
    print '<div class="warning">Aucune constante trouvée dans avimm_constante</div>';
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_quick_generation.php">Test génération</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créer un site</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
