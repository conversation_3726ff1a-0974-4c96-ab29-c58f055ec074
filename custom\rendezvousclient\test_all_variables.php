<?php
/**
 *	\file       rendezvousclient/test_all_variables.php
 *	\ingroup    rendezvousclient
 *	\brief      Test de toutes les variables corrigées
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'test_variables') {
    $results = array();
    
    // Liste des fichiers à tester
    $files_to_test = array(
        'rdv/list.php' => array('$massactionbutton', '$help_url', '$massaction'),
        'rdv/card.php' => array('$help_url', '$backtopage', '$lastkey'),
        'site/list.php' => array('$massactionbutton', '$help_url', '$massaction'),
        'site/card.php' => array('$help_url', '$backtopage', '$contactid'),
        'cdc/list.php' => array('$massactionbutton', '$help_url', '$massaction'),
        'cdc/card.php' => array('$help_url', '$backtopage'),
        'demo/list.php' => array('$help_url')
    );
    
    foreach ($files_to_test as $file => $variables) {
        $full_path = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/'.$file;
        
        if (file_exists($full_path)) {
            $content = file_get_contents($full_path);
            $missing_vars = array();
            $found_vars = array();
            
            foreach ($variables as $variable) {
                if (strpos($content, $variable) !== false) {
                    $found_vars[] = $variable;
                } else {
                    $missing_vars[] = $variable;
                }
            }
            
            if (empty($missing_vars)) {
                $results[] = array(
                    'file' => $file,
                    'status' => 'OK',
                    'details' => 'Toutes les variables trouvées: '.implode(', ', $found_vars)
                );
            } else {
                $results[] = array(
                    'file' => $file,
                    'status' => 'ERREUR',
                    'details' => 'Variables manquantes: '.implode(', ', $missing_vars)
                );
            }
        } else {
            $results[] = array(
                'file' => $file,
                'status' => 'NON TROUVÉ',
                'details' => 'Fichier non trouvé'
            );
        }
    }
    
    // Test spécifique pour les fonctions problématiques
    $specific_tests = array(
        'site/card.php' => array(
            'dol_print_phone' => 'Fonction dol_print_phone avec $contactid',
            'dol_print_email' => 'Fonction dol_print_email'
        ),
        'rdv/list.php' => array(
            '!empty($arrayfields' => 'Vérifications !empty pour $arrayfields'
        ),
        'site/list.php' => array(
            '!empty($arrayfields' => 'Vérifications !empty pour $arrayfields'
        )
    );
    
    foreach ($specific_tests as $file => $tests) {
        $full_path = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/'.$file;
        
        if (file_exists($full_path)) {
            $content = file_get_contents($full_path);
            
            foreach ($tests as $search => $description) {
                if (strpos($content, $search) !== false) {
                    $results[] = array(
                        'file' => $file,
                        'status' => 'OK',
                        'details' => $description.' - Trouvé'
                    );
                } else {
                    $results[] = array(
                        'file' => $file,
                        'status' => 'ERREUR',
                        'details' => $description.' - Non trouvé'
                    );
                }
            }
        }
    }
}

/*
 * View
 */

$title = 'Test de toutes les variables';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce test vérifie que toutes les variables nécessaires sont présentes dans tous les fichiers du module.';
print '</div>';

if ($action != 'test_variables') {
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_variables&token='.newToken().'">Tester toutes les variables</a>';
    print '</div>';
    
    print '<br><h3>Variables testées par fichier</h3>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Fichier</th>';
    print '<th>Variables vérifiées</th>';
    print '<th>Description</th>';
    print '</tr>';
    
    $test_info = array(
        'rdv/list.php' => array(
            'variables' => '$massactionbutton, $help_url, $massaction, $arrayofselected',
            'description' => 'Variables pour les listes avec actions de masse'
        ),
        'rdv/card.php' => array(
            'variables' => '$help_url, $backtopage, $lastkey',
            'description' => 'Variables pour les formulaires de rendez-vous'
        ),
        'site/list.php' => array(
            'variables' => '$massactionbutton, $help_url, $massaction',
            'description' => 'Variables pour les listes de sites'
        ),
        'site/card.php' => array(
            'variables' => '$help_url, $backtopage, $contactid',
            'description' => 'Variables pour les formulaires de sites + contact'
        ),
        'cdc/list.php' => array(
            'variables' => '$massactionbutton, $help_url, $massaction',
            'description' => 'Variables pour les listes de CDC'
        ),
        'cdc/card.php' => array(
            'variables' => '$help_url, $backtopage',
            'description' => 'Variables pour les formulaires de CDC'
        ),
        'demo/list.php' => array(
            'variables' => '$help_url',
            'description' => 'Variables pour les listes de démos'
        )
    );
    
    foreach ($test_info as $file => $info) {
        print '<tr class="oddeven">';
        print '<td><strong>'.$file.'</strong></td>';
        print '<td>'.$info['variables'].'</td>';
        print '<td>'.$info['description'].'</td>';
        print '</tr>';
    }
    
    print '</table>';
    
    print '<br><h3>Tests spécifiques</h3>';
    print '<div class="ok">';
    print '<ul>';
    print '<li><strong>Fonction dol_print_phone :</strong> Vérification que $contactid est défini</li>';
    print '<li><strong>Vérifications $arrayfields :</strong> Présence de !empty() dans les listes</li>';
    print '<li><strong>Fix $user->projet :</strong> Présence du fix dans tous les fichiers</li>';
    print '<li><strong>Variables GETPOST :</strong> Toutes les variables récupérées correctement</li>';
    print '</ul>';
    print '</div>';
    
} else {
    print '<h3>Résultats des tests</h3>';
    
    if (!empty($results)) {
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Fichier/Test</th>';
        print '<th>Statut</th>';
        print '<th>Détails</th>';
        print '</tr>';
        
        $nb_ok = 0;
        $nb_error = 0;
        $nb_not_found = 0;
        
        foreach ($results as $result) {
            print '<tr class="oddeven">';
            print '<td><strong>'.$result['file'].'</strong></td>';
            print '<td>';
            switch ($result['status']) {
                case 'OK':
                    print '<span class="badge badge-status4 badge-status">OK</span>';
                    $nb_ok++;
                    break;
                case 'ERREUR':
                    print '<span class="badge badge-status8 badge-status">ERREUR</span>';
                    $nb_error++;
                    break;
                case 'NON TROUVÉ':
                    print '<span class="badge badge-status7 badge-status">NON TROUVÉ</span>';
                    $nb_not_found++;
                    break;
            }
            print '</td>';
            print '<td>'.$result['details'].'</td>';
            print '</tr>';
        }
        
        print '</table>';
        
        print '<br>';
        if ($nb_error == 0) {
            print '<div class="ok">';
            print '<strong>Excellent !</strong> Toutes les variables sont correctement définies ('.$nb_ok.' tests OK).';
            if ($nb_not_found > 0) {
                print '<br><em>Note : '.$nb_not_found.' fichier(s) non trouvé(s) (normal si certains modules ne sont pas installés).</em>';
            }
            print '</div>';
        } else {
            print '<div class="warning">';
            print '<strong>Attention !</strong> '.$nb_error.' test(s) en erreur sur '.($nb_ok + $nb_error).' tests.';
            print '<br>Utilisez le script de correction automatique pour résoudre ces problèmes.';
            print '</div>';
        }
    }
    
    print '<div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
    print '</div>';
}

// Actions recommandées
print '<br><h3>Actions recommandées</h3>';

if ($action == 'test_variables' && !empty($results)) {
    $has_errors = false;
    foreach ($results as $result) {
        if ($result['status'] == 'ERREUR') {
            $has_errors = true;
            break;
        }
    }
    
    if ($has_errors) {
        print '<div class="warning">';
        print '<strong>Des erreurs ont été détectées.</strong> Utilisez le script de correction automatique :';
        print '</div>';
        print '<div class="center">';
        print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/fix_all_missing_variables.php">Corriger automatiquement</a>';
        print '</div>';
    } else {
        print '<div class="ok">';
        print '<strong>Aucune erreur détectée.</strong> Vous pouvez maintenant utiliser le module sans problème.';
        print '</div>';
        print '<div class="center">';
        print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_final_fix.php">Lancer le test final complet</a>';
        print '</div>';
    }
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/fix_all_missing_variables.php">Correction automatique</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_site_class.php">Test classe Site</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_rendezvous_class.php">Test classe Rendezvous</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
