<?php
/**
 *	\file       rendezvousclient/diagnostic_generation.php
 *	\ingroup    rendezvousclient
 *	\brief      Diagnostic complet du système de génération
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$title = 'Diagnostic du système de génération de documents';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce diagnostic vérifie tous les éléments nécessaires au bon fonctionnement de la génération de documents ODT.';
print '</div>';

// 1. Vérification des extensions PHP
print '<h3>1. Extensions PHP requises</h3>';

$php_extensions = array(
    'ZipArchive' => class_exists('ZipArchive'),
    'SimpleXML' => extension_loaded('simplexml'),
    'DOM' => extension_loaded('dom'),
    'libxml' => extension_loaded('libxml')
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Extension</th>';
print '<th>Statut</th>';
print '<th>Description</th>';
print '</tr>';

foreach ($php_extensions as $ext => $available) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$ext.'</strong></td>';
    print '<td>';
    if ($available) {
        print '<span class="badge badge-status4 badge-status">OK</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">MANQUANT</span>';
    }
    print '</td>';
    print '<td>';
    switch ($ext) {
        case 'ZipArchive':
            print $available ? 'Extension disponible pour manipuler les fichiers ODT' : 'Extension requise pour créer/modifier les fichiers ODT';
            break;
        case 'SimpleXML':
            print $available ? 'Extension disponible pour traiter le XML' : 'Extension requise pour traiter le contenu XML des ODT';
            break;
        case 'DOM':
            print $available ? 'Extension disponible pour manipuler le DOM' : 'Extension requise pour manipuler le contenu XML';
            break;
        case 'libxml':
            print $available ? 'Extension disponible pour le support XML' : 'Extension requise pour le support XML de base';
            break;
    }
    print '</td>';
    print '</tr>';
}

print '</table>';

// 2. Vérification des répertoires
print '<br><h3>2. Répertoires et permissions</h3>';

$directories = array(
    'Templates' => DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/',
    'Output' => DOL_DATA_ROOT.'/rendezvousclient/',
    'Sites' => DOL_DATA_ROOT.'/rendezvousclient/sites/'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Répertoire</th>';
print '<th>Chemin</th>';
print '<th>Existe</th>';
print '<th>Écriture</th>';
print '</tr>';

foreach ($directories as $name => $path) {
    $exists = is_dir($path);
    $writable = $exists ? is_writable($path) : false;
    
    print '<tr class="oddeven">';
    print '<td><strong>'.$name.'</strong></td>';
    print '<td><code>'.$path.'</code></td>';
    print '<td>';
    if ($exists) {
        print '<span class="badge badge-status4 badge-status">OUI</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">NON</span>';
    }
    print '</td>';
    print '<td>';
    if ($writable) {
        print '<span class="badge badge-status4 badge-status">OUI</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">NON</span>';
    }
    print '</td>';
    print '</tr>';
}

print '</table>';

// 3. Vérification des classes
print '<br><h3>3. Classes et fichiers</h3>';

$classes = array(
    'DocumentGenerator' => DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/class/document_generator.class.php',
    'Site' => DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/site/class/site.class.php',
    'Societe' => DOL_DOCUMENT_ROOT.'/societe/class/societe.class.php',
    'Project' => DOL_DOCUMENT_ROOT.'/projet/class/project.class.php'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Classe</th>';
print '<th>Fichier</th>';
print '<th>Existe</th>';
print '<th>Chargeable</th>';
print '</tr>';

foreach ($classes as $class => $file) {
    $exists = file_exists($file);
    $loadable = false;
    
    if ($exists) {
        try {
            if ($class == 'DocumentGenerator') {
                dol_include_once('/rendezvousclient/core/class/document_generator.class.php');
                $loadable = class_exists('DocumentGenerator');
            } else {
                $loadable = class_exists($class);
            }
        } catch (Exception $e) {
            $loadable = false;
        }
    }
    
    print '<tr class="oddeven">';
    print '<td><strong>'.$class.'</strong></td>';
    print '<td><code>'.basename($file).'</code></td>';
    print '<td>';
    if ($exists) {
        print '<span class="badge badge-status4 badge-status">OUI</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">NON</span>';
    }
    print '</td>';
    print '<td>';
    if ($loadable) {
        print '<span class="badge badge-status4 badge-status">OUI</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">NON</span>';
    }
    print '</td>';
    print '</tr>';
}

print '</table>';

// 4. Test de création d'un fichier ODT simple
print '<br><h3>4. Test de création ODT</h3>';

$test_odt_result = false;
$test_odt_message = '';

if (class_exists('ZipArchive')) {
    try {
        $test_file = DOL_DATA_ROOT.'/rendezvousclient/test_creation.odt';
        
        // Créer le répertoire si nécessaire
        $test_dir = dirname($test_file);
        if (!is_dir($test_dir)) {
            dol_mkdir($test_dir);
        }
        
        $zip = new ZipArchive();
        if ($zip->open($test_file, ZipArchive::CREATE) === TRUE) {
            $zip->addFromString('mimetype', 'application/vnd.oasis.opendocument.text');
            $zip->addFromString('content.xml', '<?xml version="1.0" encoding="UTF-8"?><office:document-content xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0"><office:body><office:text><text:p>Test de création ODT</text:p></office:text></office:body></office:document-content>');
            $zip->close();
            
            if (file_exists($test_file)) {
                $test_odt_result = true;
                $test_odt_message = 'Fichier ODT créé avec succès ('.filesize($test_file).' octets)';
                // Nettoyer
                unlink($test_file);
            } else {
                $test_odt_message = 'Fichier ODT non créé après fermeture du ZIP';
            }
        } else {
            $test_odt_message = 'Impossible d\'ouvrir le fichier ZIP pour écriture';
        }
    } catch (Exception $e) {
        $test_odt_message = 'Exception lors de la création: ' . $e->getMessage();
    }
} else {
    $test_odt_message = 'Extension ZipArchive non disponible';
}

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Test</th>';
print '<th>Résultat</th>';
print '<th>Détails</th>';
print '</tr>';

print '<tr class="oddeven">';
print '<td><strong>Création fichier ODT</strong></td>';
print '<td>';
if ($test_odt_result) {
    print '<span class="badge badge-status4 badge-status">SUCCÈS</span>';
} else {
    print '<span class="badge badge-status8 badge-status">ÉCHEC</span>';
}
print '</td>';
print '<td>'.$test_odt_message.'</td>';
print '</tr>';

print '</table>';

// 5. Vérification des données de test
print '<br><h3>5. Données de test disponibles</h3>';

$sql = "SELECT COUNT(*) as nb_sites FROM ".MAIN_DB_PREFIX."rendez_vous_site";
$resql = $db->query($sql);
$nb_sites = 0;
if ($resql) {
    $obj = $db->fetch_object($resql);
    $nb_sites = $obj->nb_sites;
}

$sql = "SELECT COUNT(*) as nb_societes FROM ".MAIN_DB_PREFIX."societe WHERE client IN (1,3)";
$resql = $db->query($sql);
$nb_societes = 0;
if ($resql) {
    $obj = $db->fetch_object($resql);
    $nb_societes = $obj->nb_societes;
}

$sql = "SELECT COUNT(*) as nb_projets FROM ".MAIN_DB_PREFIX."projet";
$resql = $db->query($sql);
$nb_projets = 0;
if ($resql) {
    $obj = $db->fetch_object($resql);
    $nb_projets = $obj->nb_projets;
}

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Type de données</th>';
print '<th>Nombre</th>';
print '<th>Statut</th>';
print '</tr>';

$data_types = array(
    'Sites' => $nb_sites,
    'Sociétés clientes' => $nb_societes,
    'Projets' => $nb_projets
);

foreach ($data_types as $type => $count) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$type.'</strong></td>';
    print '<td>'.$count.'</td>';
    print '<td>';
    if ($count > 0) {
        print '<span class="badge badge-status4 badge-status">DISPONIBLE</span>';
    } else {
        print '<span class="badge badge-status7 badge-status">AUCUNE</span>';
    }
    print '</td>';
    print '</tr>';
}

print '</table>';

// 6. Résumé et recommandations
print '<br><h3>6. Résumé et recommandations</h3>';

$issues = array();
$recommendations = array();

if (!class_exists('ZipArchive')) {
    $issues[] = 'Extension ZipArchive manquante';
    $recommendations[] = 'Installer l\'extension PHP ZipArchive';
}

if (!is_dir(DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/')) {
    $issues[] = 'Répertoire templates manquant';
    $recommendations[] = 'Créer le répertoire des templates';
}

if (!is_dir(DOL_DATA_ROOT.'/rendezvousclient/')) {
    $issues[] = 'Répertoire de sortie manquant';
    $recommendations[] = 'Créer le répertoire de sortie des documents';
}

if (!$test_odt_result) {
    $issues[] = 'Impossible de créer un fichier ODT';
    $recommendations[] = 'Vérifier les permissions et l\'extension ZipArchive';
}

if ($nb_sites == 0) {
    $issues[] = 'Aucun site pour tester';
    $recommendations[] = 'Créer au moins un site avec projet et société';
}

if (empty($issues)) {
    print '<div class="ok">';
    print '<strong>✅ Système prêt !</strong><br>';
    print 'Tous les prérequis sont satisfaits. Vous pouvez utiliser la génération de documents.';
    print '</div>';
} else {
    print '<div class="warning">';
    print '<strong>⚠️ Problèmes détectés :</strong><br>';
    foreach ($issues as $issue) {
        print '• ' . $issue . '<br>';
    }
    print '</div>';
    
    print '<div class="info">';
    print '<strong>📋 Recommandations :</strong><br>';
    foreach ($recommendations as $rec) {
        print '• ' . $rec . '<br>';
    }
    print '</div>';
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_simple_generation.php">Test simple</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/admin/templates.php">Gestion templates</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créer un site</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
