<?php
/* Copyright (C) 2001-2005 <PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2004-2015 <PERSON>  <<EMAIL>>
 * Copyright (C) 2005-2012 <PERSON>        <<EMAIL>>
 * Copyright (C) 2015      <PERSON><PERSON><PERSON>	<<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/*
 *	\file       constanteavimm/constante/list.php
 *	\ingroup    constanteavimm
 *	\brief      liste des constantes
*/

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) {
	$res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
}
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) {
	$i--; $j--;
}
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) {
	$res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
}
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) {
	$res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
}
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) {
	$res = @include "../main.inc.php";
}
if (!$res && file_exists("../../main.inc.php")) {
	$res = @include "../../main.inc.php";
}
if (!$res && file_exists("../../../main.inc.php")) {
	$res = @include "../../../main.inc.php";
}
if (!$res) {
	die("Include of main fails");
}

require_once DOL_DOCUMENT_ROOT.'/custom/constanteavimm/constante/class/constante.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';

// Load translation files required by the page
$langs->loadLangs(array('constanteavimm'));

$search_id = GETPOST('search_id');
$search_libelle = GETPOST('search_libelle');
$search_description = GETPOST('search_description');
$search_fk_module = GETPOST('search_fk_module');
$search_fk_logiciel = GETPOST('search_fk_logiciel');
$search_valeur = GETPOST('search_valeur');
$search_de_base = GETPOST('search_de_base');

// Load variable for pagination
$limit = GETPOST('limit', 'int') ?GETPOST('limit', 'int') : $conf->liste_limit;
$sortfield = GETPOST('sortfield', 'aZ09comma');
$sortorder = GETPOST('sortorder', 'aZ09comma');
$page = GETPOSTISSET('pageplusone') ? (GETPOST('pageplusone') - 1) : GETPOST("page", 'int');
if (empty($page) || $page < 0 || GETPOST('button_search', 'alpha') || GETPOST('button_removefilter', 'alpha')) {
	$page = 0;
}     // If $page is not defined, or '' or -1 or if we click on clear filters
$offset = $limit * $page;
$pageprev = $page - 1;
$pagenext = $page + 1;
if (!$sortorder) {
    $sortorder = "DESC";
}
if (!$sortfield) {
    $sortfield = "ac.rowid";
}

$object = new Constante($db);

$arrayfields = array(
    'ac.rowid' => array('label'=>"TechnicalID", 'position'=>1, 'checked'=>(!empty($conf->global->MAIN_SHOW_TECHNICAL_ID)), 'enabled'=>(!empty($conf->global->MAIN_SHOW_TECHNICAL_ID))),
    'ac.libelle' =>array('label'=>"libelle", 'position'=>2, 'checked'=>1),
    'ac.description' => array('label'=>"description", 'position'=>3, 'checked'=>1),
    'acm.rowid' => array('label'=>"module", 'position'=>4, 'checked'=>1),
    'acl.rowid' => array('label'=>"logiciel", 'position'=>5, 'checked'=>1),
    'ac.valeur' => array('label'=>"value", 'position'=>6, 'checked'=>1),
    'ac.debase' => array('label'=>"debase", 'position'=>7, 'checked'=>1),
);


/*
 * Actions
*/ 
// Selection of new fields
include DOL_DOCUMENT_ROOT.'/core/actions_changeselectedfields.inc.php';

// Do we click on purge search criteria ?
if (GETPOST('button_removefilter_x', 'alpha') || GETPOST('button_removefilter.x', 'alpha') || GETPOST('button_removefilter', 'alpha')) { // All tests are required to be compatible with all browsers
    $search_id = "";
    $search_libelle = "";
    $search_description = "";
    $search_fk_module = "";
    $search_fk_logiciel = "";
    $search_valeur = "";
    $search_de_base = "";
    // remet le tri par defaut
    $sortorder = "DESC";
    $sortfield = "ac.rowid";
}


/*
 * View
*/ 

$title = $langs->trans('constantes');

llxHeader('', $title, $help_url);

$sql = "SELECT ac.rowid as const_id, ac.libelle as libelle, ac.valeur, ac.dependance_sql, ac.description, ac.debase, acm.libelle as module, acl.rowid as fk_logiciel, acl.libelle as logiciel";
$sql .= " FROM ".MAIN_DB_PREFIX."avimm_constante ac";
$sql .= " LEFT OUTER JOIN ".MAIN_DB_PREFIX."avimm_constante_inmodule acim ON acim.fk_constante = ac.rowid";
$sql .= " LEFT OUTER JOIN ".MAIN_DB_PREFIX."avimm_constante_module acm ON acm.rowid = acim.fk_module";
$sql .= " LEFT OUTER JOIN ".MAIN_DB_PREFIX."avimm_constante_logiciel acl ON acl.rowid = acm.fk_logiciel";
$sql .= " WHERE 1=1";
if($search_id > 0){
    $sql .= ' AND ac.rowid = '.$search_id;
}
if($search_libelle){
    $sql .= natural_search('ac.libelle', $search_libelle);
}
if($search_description){
    $sql .= natural_search('ac.description', $search_description);
}
if($search_fk_module > 0){
    $sql .= ' AND acm.rowid = '.$search_fk_module;
}
if($search_fk_logiciel > 0){
    $sql .= ' AND acl.rowid = '.$search_fk_logiciel;
}
if($search_valeur){
    $sql .= natural_search('ac.valeur', $search_valeur);
}
if($search_de_base == '0' || $search_de_base == '1'){
    $sql .= ' AND ac.debase = '.$search_de_base;
}

$sql .= $db->order($sortfield, $sortorder);

$result = $db->query($sql);
$nbtotalofrecords = $db->num_rows($result);

if (($page * $limit) > $nbtotalofrecords) {	// if total resultset is smaller then paging size (filtering), goto and load page 0
    $page = 0;
    $offset = 0;
}

$sql .= $db->plimit($limit + 1, $offset);
$resql = $db->query($sql);
if (!$resql) {
    dol_print_error($db);
    exit;
}
$num = $db->num_rows($resql);

$param = '';
if ($limit > 0 && $limit != $conf->liste_limit) {
    $param .= '&limit='.urlencode($limit);
}
if($search_id > 0){
    $param .= '&search_id='.urlencode($search_id);
}
if($search_libelle){
    $param .= '&search_libelle='.urlencode($search_libelle);
}
if($search_description){
    $param .= '&search_description='.urlencode($search_description);
}
if($search_fk_module){
    $param .= '&search_fk_module='.urlencode($search_fk_module);
}
if($search_fk_logiciel){
    $param .= '&search_fk_logiciel='.urlencode($search_fk_logiciel);
}
if($search_valeur){
    $param .= '&search_valeur='.urlencode($search_valeur);
}
if($search_de_base >= 0){
    $param .= '&search_de_base='.urlencode($search_de_base);
}

$form = new Form($db);

print '<form method="POST" id="searchFormList" action="'.$_SERVER["PHP_SELF"].($socid>0?'?socid='.$socid:'').'">';
print '<input type="hidden" name="token" value="'.newToken().'">';
print '<input type="hidden" name="action" value="list">';
print '<input type="hidden" name="formfilteraction" id="formfilteraction" value="list">';
print '<input type="hidden" name="sortfield" value="'.$sortfield.'">';
print '<input type="hidden" name="sortorder" value="'.$sortorder.'">';

print_barre_liste($langs->trans('constantes'), $page, $_SERVER["PHP_SELF"], $param, $sortfield, $sortorder, $massactionbutton, $num, $nbtotalofrecords, 'tools', 0, $newcardbutton, '', $limit, 0, 0, 1);

$varpage = empty($contextpage) ? $_SERVER["PHP_SELF"] : $contextpage;
$selectedfields = $form->multiSelectArrayWithCheckbox('selectedfields', $arrayfields, $varpage);
// $selectedfields .= $form->showCheckAddButtons('checkforselect', 1);

print '<div class="div-table-responsive" style="overflow-x: unset">';
print '<table class="tagtable liste">'."\n";

print '<tr class="liste_titre_filter" id="secondsticky" style="position: sticky; z-index: 1000;">';
print '<script>
$(document).ready(function(){
    var top = $("#id-top").outerHeight(true);
    $("#secondsticky").css("top", Math.floor(top)+"px");
});
</script>';

if(!empty($arrayfields['ac.rowid']['checked'])){
    print '<td class="liste_titre" data-key="id">';
    print '<input class="flat searchstring" type="text" name="search_id" size="1" value="'.dol_escape_htmltag($search_id).'">';
    print '</td>';
}
if(!empty($arrayfields['ac.libelle']['checked'])){
    print '<td class="liste_titre" data-key="nom">';
    print '<input class="flat searchstring" type="text" name="search_libelle" size="30" value="'.dol_escape_htmltag($search_libelle).'">';
    print '</td>';
}
if(!empty($arrayfields['ac.description']['checked'])){
    print '<td class="liste_titre" data-key="description">';
    print '<input class="flat searchstring" type="text" name="search_description" size="30" value="'.dol_escape_htmltag($search_description).'">';
    print '</td>';
}
if(!empty($arrayfields['acm.rowid']['checked'])){
    print '<td class="liste_titre" data-key="fk_module" style="width: 300px;">';
    print $object->listeModule($search_fk_module, 'search_fk_module', 'search_fk_module', '', 30);
    print '</td>';
}
if(!empty($arrayfields['acl.rowid']['checked'])){
    print '<td class="liste_titre" data-key="fk_logiciel">';
    print $object->listeLogiciel($search_fk_logiciel, 'search_fk_logiciel', 1);
    print '</td>';
}
if(!empty($arrayfields['ac.valeur']['checked'])){
    print '<td class="liste_titre" data-key="valeur">';
    print '<input class="flat searchstring" type="text" name="search_valeur" size="10" value="'.dol_escape_htmltag($search_valeur).'">';
    print '</td>';
}
if(!empty($arrayfields['ac.debase']['checked'])){
    print '<td class="liste_titre" data-key="debase">';
    print $form->selectarray('search_de_base', array(0 => 'Non', 1 => 'Oui'), $search_de_base, 1);
    print '</td>';
}

// Action column
print '<td class="liste_titre" align="middle">';
$searchpicto = $form->showFilterButtons();
print $searchpicto;
print '</td>';
print '</tr>';

print '<tr class="liste_titre" id="thirdsticky" style="position: sticky; z-index: 1000;">';
print '<script>
$(document).ready(function(){
    var top = $("#id-top").outerHeight(true) + $("#secondsticky").outerHeight(true);
    $("#thirdsticky").css("top", Math.floor(top)+"px");
});
</script>';

if(!empty($arrayfields['ac.rowid']['checked'])){
    print_liste_field_titre($arrayfields['ac.rowid']['label'], $_SERVER["PHP_SELF"], "id", "", $param, ' data-key="id"', $sortfield, $sortorder, 'actioncolumn ');
}
if(!empty($arrayfields['ac.libelle']['checked'])){
    print_liste_field_titre($arrayfields['ac.libelle']['label'], $_SERVER["PHP_SELF"], "libelle", "", $param, ' data-key="libelle"', $sortfield, $sortorder, 'actioncolumn ');
}
if(!empty($arrayfields['ac.description']['checked'])){
    print_liste_field_titre($arrayfields['ac.description']['label'], $_SERVER["PHP_SELF"], "description", "", $param, ' data-key="description"', $sortfield, $sortorder, 'actioncolumn ');
}
if(!empty($arrayfields['acm.rowid']['checked'])){
    print_liste_field_titre($arrayfields['acm.rowid']['label'], $_SERVER["PHP_SELF"], "fk_module", "", $param, ' data-key="fk_module"', $sortfield, $sortorder, 'actioncolumn ');
}
if(!empty($arrayfields['acl.rowid']['checked'])){
    print_liste_field_titre($arrayfields['acl.rowid']['label'], $_SERVER["PHP_SELF"], "fk_logiciel", "", $param, ' data-key="fk_logiciel"', $sortfield, $sortorder, 'actioncolumn ');
}
if(!empty($arrayfields['ac.valeur']['checked'])){
    print_liste_field_titre($arrayfields['ac.valeur']['label'], $_SERVER["PHP_SELF"], "valeur", "", $param, ' data-key="valeur"', $sortfield, $sortorder, 'actioncolumn ');
}
if(!empty($arrayfields['ac.debase']['checked'])){
    print_liste_field_titre($arrayfields['ac.debase']['label'], $_SERVER["PHP_SELF"], "debase", "", $param, ' data-key="debase"', $sortfield, $sortorder, 'actioncolumn ');
}
print_liste_field_titre($selectedfields, $_SERVER["PHP_SELF"], "", '', '', '', $sortfield, $sortorder, 'center maxwidthsearch actioncolumn ');
print "</tr>\n";

if($resql){
    $i = 0;
    $imaxinloop = ($limit ? min($num, $limit) : $num);
    while ($i < $imaxinloop) {
        $obj = $db->fetch_object($resql);
        $object->fetch($obj->const_id);
        print "<tr onclick=\"window.location='card.php?id=".$obj->const_id."&save_lastsearch_values=1'\" class=\"oddeven\">";

        if(!empty($arrayfields['ac.rowid']['checked'])){
            print '<td class="tdoverflowmax50" data-key="id">';
            print $obj->const_id;
            print '</td>';
        }

        if(!empty($arrayfields['ac.libelle']['checked'])){
            print '<td class="tdoverflowmax50" data-key="libelle" title="'.$obj->libelle.'">';
            print $object->getNomUrl();
            print '</td>';
        }
        if(!empty($arrayfields['ac.description']['checked'])){
            print '<td class="tdoverflowmax50" data-key="description" title="'.htmlspecialchars($obj->description).'">';
            print htmlspecialchars($obj->description);
            // print $obj->description;
            print '</td>';
        }
        if(!empty($arrayfields['acm.rowid']['checked'])){
            print '<td class="tdoverflowmax50" data-key="fk_module" title="'.$obj->module.'">';
            print $obj->module;
            print '</td>';
        }
        if(!empty($arrayfields['acl.rowid']['checked'])){
            print '<td class="tdoverflowmax50" data-key="fk_logiciel">';
            print $obj->logiciel;
            print '</td>';
        }
        if(!empty($arrayfields['ac.valeur']['checked'])){
            print '<td class="tdoverflowmax50" data-key="valeur" title="'.$obj->valeur.'">';
            print $obj->valeur;
            print '</td>';
        }
        if(!empty($arrayfields['ac.debase']['checked'])){
            print '<td class="tdoverflowmax50" data-key="debase">';
            print ($obj->debase == 1 ? 'Oui' : 'Non');
            print '</td>';
        }

        print "<script>
        function tdclick(e){
            if (!e) var e = window.event;                // Get the window event
            e.cancelBubble = true;                       // IE Stop propagationif (e.stopPropagation) 
            e.stopPropagation();  // Other Broswers
        }
        </script>";
        
        print '<td onclick="tdclick(event)" class="nowrap inline">';
        print '</td>';

        print '</tr>'."\n";

        $i++;
    }
}

print '</div>';
print '</table>';

// End of page
llxFooter();
$db->close();
