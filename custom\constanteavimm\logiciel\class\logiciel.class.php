<?php
/*
 *  \file       constanteavimm/logiciel/class/logiciel.class.php
 *  \ingroup    constanteavimm
 *  \brief      Fichier des classes de logiciel
*/


class Logiciel
{
    /**
	 * @var string ID to identify managed object
	 */
	public $element = 'avimm_constante_logiciel';

	/**
	 * @var string Name of table without prefix where object is stored
	 */
	public $table_element = 'avimm_constante_logiciel';

    public $picto = 'tools';

	public $db;

	public $error;

    public $id;

    public $libelle;

	public $cahier_des_charges;

	public $tarif;

    /**
	 *	Constructor
	 *
	 *  @param		DoliDB		$db      Database handler
	 */
	public function __construct($db)
	{
		$this->db = $db;
	}

    /**
	 *	Create logiciel
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function create()
	{
		global $conf, $langs;

        $this->db->begin();

        $sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_logiciel(libelle, cahier_des_charges, tarif)";
        $sql .= " VALUES(";
        $sql .= " '".$this->db->escape($this->libelle)."'";
		$sql .= ", '".$this->db->escape($this->cahier_des_charges)."'";
		$sql .= ", '".price2num($this->tarif, 'MU')."'";
        $sql .= ")";
        
        $resql = $this->db->query($sql);
		if($resql){
			$this->id = $this->db->last_insert_id(MAIN_DB_PREFIX.'avimm_constante_logiciel');

			if ($this->id) {
                $this->fetch($this->id);

                $this->db->commit();
                return 1;
            }
        }else{
			dol_print_error($this->db);
			$this->db->rollback();
			return -1;
		}
    }

    /**
	 *	Fetch logiciel
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function fetch($id)
	{
		// Check parameters
		if(empty($id)){
			return -1;
		}

        $sql = "SELECT rowid, libelle, cahier_des_charges, tarif FROM ".MAIN_DB_PREFIX."avimm_constante_logiciel";
        $sql .= " WHERE rowid = ".$id;

        $resql = $this->db->query($sql);
		if($resql){
			$obj = $this->db->fetch_object($resql);

            if($obj){
                $this->id = $obj->rowid;
				$this->libelle = $obj->libelle;
				$this->cahier_des_charges = $obj->cahier_des_charges;
				$this->tarif = $obj->tarif;
            }else {
				$this->error = 'Logiciel with id '.$id.' not found sql='.$sql;
				return 0;
			}

        }else{
			$this->error = $this->db->error();
			return -1;
		}
    }

	/**
	 *	Update logiciel
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function update($id){

		// Check parameters
		if(empty($id)){
			return -1;
		}

		$sql = "UPDATE ".MAIN_DB_PREFIX."avimm_constante_logiciel SET";
		$sql .= " libelle = '".$this->db->escape($this->libelle)."'";
		$sql .= ", cahier_des_charges = '".$this->db->escape($this->cahier_des_charges)."'";
		$sql .= ", tarif = '".price2num($this->tarif, 'MU')."'";
		$sql .= " WHERE rowid = ".$id;

		$this->db->begin();

		$resql = $this->db->query($sql);
		
		if($resql){
			$this->db->commit();
			return 1;
		}else{
			$this->error = $this->db->error();
			$this->db->rollback();
			return -1;
		}
	}

	/**
	 *	Delete logiciel
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
    public function delete($id){

        // Check parameters
		if(empty($id)){
			return -1;
		}

        $this->db->begin();

        $sql = "DELETE FROM ".MAIN_DB_PREFIX."avimm_constante_logiciel WHERE rowid = ".$id;
        
        $resql = $this->db->query($sql);
        if($resql){
            $this->db->commit();
            
            return 1;
        }else{
			$this->error = $this->db->error();
            $this->db->rollback();
			return -1;
        }
    }

    public function getNomUrl(){

        $url = DOL_URL_ROOT.'/custom/constanteavimm/logiciel/card.php?id='.$this->id;

        $link = '<a href="'.$url.'">';
        $link .= $this->libelle;
        $link .= '</a>';

        return $link;
    }
}