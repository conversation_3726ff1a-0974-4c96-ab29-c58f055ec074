<?php
/**
 *	\file       rendezvousclient/test_document_generation.php
 *	\ingroup    rendezvousclient
 *	\brief      Test de génération de documents ODT
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

dol_include_once('/rendezvousclient/core/class/document_generator.class.php');
dol_include_once('/rendezvousclient/site/class/site.class.php');

$action = GETPOST('action', 'aZ09');
$site_id = GETPOST('site_id', 'int');

/*
 * Actions
 */

if ($action == 'test_generation' && $site_id > 0) {
    $site = new Site($db);
    $result = $site->fetch($site_id);
    
    if ($result > 0) {
        $generator = new DocumentGenerator($db);
        
        // Test de génération pour chaque type de document
        $document_types = array(
            'cahier_charges' => 'Cahier des charges',
            'proposition_technique' => 'Proposition technique',
            'devis' => 'Devis',
            'synthese_cdc' => 'Synthèse CDC'
        );
        
        $results = array();
        
        foreach ($document_types as $type => $name) {
            try {
                $output_file = $generator->generateSiteDocument($site, $type);
                
                if ($output_file) {
                    $results[] = array(
                        'type' => $name,
                        'status' => 'OK',
                        'file' => $output_file,
                        'size' => filesize($output_file),
                        'details' => 'Document généré avec succès'
                    );
                } else {
                    $results[] = array(
                        'type' => $name,
                        'status' => 'ERREUR',
                        'file' => '',
                        'size' => 0,
                        'details' => $generator->error ?: 'Erreur inconnue'
                    );
                }
            } catch (Exception $e) {
                $results[] = array(
                    'type' => $name,
                    'status' => 'ERREUR',
                    'file' => '',
                    'size' => 0,
                    'details' => 'Exception: ' . $e->getMessage()
                );
            }
        }
    } else {
        setEventMessages("Site non trouvé", null, 'errors');
    }
}

if ($action == 'create_test_templates') {
    $templates_dir = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/';
    if (!is_dir($templates_dir)) {
        dol_mkdir($templates_dir);
    }
    
    $generator = new DocumentGenerator($db);
    $created = 0;
    
    // Créer les templates par défaut
    $template_types = array('cahier_charges', 'proposition_technique', 'devis', 'synthese_cdc');
    
    foreach ($template_types as $type) {
        if (createDefaultTemplate($type, $templates_dir)) {
            $created++;
        }
    }
    
    if ($created > 0) {
        setEventMessages("$created template(s) par défaut créé(s)", null, 'mesgs');
    } else {
        setEventMessages("Erreur lors de la création des templates", null, 'errors');
    }
}

/**
 * Crée un template par défaut (copié de admin/templates.php)
 */
function createDefaultTemplate($type, $templates_dir)
{
    $template_names = array(
        'cahier_charges' => 'cahier_des_charges_template.odt',
        'proposition_technique' => 'proposition_technique_template.odt',
        'devis' => 'devis_template.odt',
        'synthese_cdc' => 'synthese_cdc_template.odt'
    );
    
    if (!isset($template_names[$type])) {
        return false;
    }
    
    $filename = $templates_dir . $template_names[$type];
    
    // Ne pas écraser un template existant
    if (file_exists($filename)) {
        return true;
    }
    
    // Contenu XML basique pour un document ODT
    $content_xml = '<?xml version="1.0" encoding="UTF-8"?>
<office:document-content xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0">
<office:body>
<office:text>
<text:h text:style-name="Heading_20_1" text:outline-level="1">'.getTemplateTitle($type).'</text:h>
<text:p text:style-name="Standard"/>
<text:p text:style-name="Standard">Date de génération : __DATE_GENERATION__</text:p>
<text:p text:style-name="Standard">Généré par : __UTILISATEUR__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Client</text:h>
<text:p text:style-name="Standard">Société : __SOCIETE_NOM__</text:p>
<text:p text:style-name="Standard">Adresse : __SOCIETE_ADRESSE__</text:p>
<text:p text:style-name="Standard">__SOCIETE_CP__ __SOCIETE_VILLE__</text:p>
<text:p text:style-name="Standard">Téléphone : __SOCIETE_TEL__</text:p>
<text:p text:style-name="Standard">Email : __SOCIETE_EMAIL__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Site</text:h>
<text:p text:style-name="Standard">Nom du site : __SITE_NOM__</text:p>
<text:p text:style-name="Standard">Type : __SITE_TYPE__</text:p>
<text:p text:style-name="Standard">Description : __SITE_DESCRIPTION__</text:p>
<text:p text:style-name="Standard">Nombre d\'utilisateurs : __SITE_NB_UTILISATEURS__</text:p>
<text:p text:style-name="Standard">Logiciel : __SITE_LOGICIEL__</text:p>
<text:p text:style-name="Standard">Hébergement : __SITE_HEBERGEMENT__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Utilisateurs</text:h>
<text:p text:style-name="Standard">__SITE_UTILISATEURS__</text:p>

</office:text>
</office:body>
</office:document-content>';
    
    // Créer un fichier ZIP (ODT)
    $zip = new ZipArchive();
    if ($zip->open($filename, ZipArchive::CREATE) !== TRUE) {
        return false;
    }
    
    // Ajouter les fichiers nécessaires
    $zip->addFromString('mimetype', 'application/vnd.oasis.opendocument.text');
    $zip->addFromString('content.xml', $content_xml);
    
    // META-INF/manifest.xml
    $manifest = '<?xml version="1.0" encoding="UTF-8"?>
<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0">
<manifest:file-entry manifest:full-path="/" manifest:media-type="application/vnd.oasis.opendocument.text"/>
<manifest:file-entry manifest:full-path="content.xml" manifest:media-type="text/xml"/>
</manifest:manifest>';
    
    $zip->addFromString('META-INF/manifest.xml', $manifest);
    
    $zip->close();
    
    return true;
}

function getTemplateTitle($type)
{
    $titles = array(
        'cahier_charges' => 'Cahier des Charges',
        'proposition_technique' => 'Proposition Technique et Commerciale',
        'devis' => 'Devis',
        'synthese_cdc' => 'Synthèse du Cahier des Charges'
    );
    
    return isset($titles[$type]) ? $titles[$type] : 'Document';
}

/*
 * View
 */

$title = 'Test de génération de documents ODT';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script teste la génération de documents ODT à partir des informations des sites.';
print '</div>';

// Vérifier les prérequis
print '<h3>Vérification des prérequis</h3>';

$prerequisites = array();

// Vérifier ZipArchive
$prerequisites[] = array(
    'test' => 'Extension ZipArchive',
    'status' => class_exists('ZipArchive') ? 'OK' : 'ERREUR',
    'details' => class_exists('ZipArchive') ? 'Extension disponible' : 'Extension ZipArchive non disponible'
);

// Vérifier le répertoire templates
$templates_dir = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/';
$prerequisites[] = array(
    'test' => 'Répertoire templates',
    'status' => is_dir($templates_dir) ? 'OK' : 'ERREUR',
    'details' => is_dir($templates_dir) ? 'Répertoire existe' : 'Répertoire manquant'
);

// Vérifier les templates
$template_files = array(
    'cahier_des_charges_template.odt',
    'proposition_technique_template.odt',
    'devis_template.odt',
    'synthese_cdc_template.odt'
);

$templates_found = 0;
foreach ($template_files as $template) {
    if (file_exists($templates_dir . $template)) {
        $templates_found++;
    }
}

$prerequisites[] = array(
    'test' => 'Templates disponibles',
    'status' => $templates_found > 0 ? 'OK' : 'ERREUR',
    'details' => "$templates_found template(s) trouvé(s) sur " . count($template_files)
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Test</th>';
print '<th>Statut</th>';
print '<th>Détails</th>';
print '</tr>';

foreach ($prerequisites as $prereq) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$prereq['test'].'</strong></td>';
    print '<td>';
    if ($prereq['status'] == 'OK') {
        print '<span class="badge badge-status4 badge-status">OK</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">ERREUR</span>';
    }
    print '</td>';
    print '<td>'.$prereq['details'].'</td>';
    print '</tr>';
}

print '</table>';

// Bouton pour créer les templates par défaut
if ($templates_found == 0) {
    print '<br><div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=create_test_templates&token='.newToken().'">Créer les templates par défaut</a>';
    print '</div>';
}

// Sélection du site pour test
if (!$site_id) {
    print '<br><h3>Sélectionner un site pour tester</h3>';
    
    $sql = "SELECT s.rowid, s.nom, soc.nom as nom_societe 
            FROM ".MAIN_DB_PREFIX."rendez_vous_site s
            LEFT JOIN ".MAIN_DB_PREFIX."societe soc ON s.fk_projet IN (SELECT rowid FROM ".MAIN_DB_PREFIX."projet WHERE socid = soc.rowid)
            ORDER BY s.nom LIMIT 10";
    
    $resql = $db->query($sql);
    
    if ($resql && $db->num_rows($resql) > 0) {
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Site</th>';
        print '<th>Société</th>';
        print '<th>Action</th>';
        print '</tr>';
        
        while ($obj = $db->fetch_object($resql)) {
            print '<tr class="oddeven">';
            print '<td><strong>'.$obj->nom.'</strong></td>';
            print '<td>'.$obj->nom_societe.'</td>';
            print '<td>';
            print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?site_id='.$obj->rowid.'">Tester</a>';
            print '</td>';
            print '</tr>';
        }
        
        print '</table>';
    } else {
        print '<div class="warning">Aucun site trouvé. Créez d\'abord un site pour tester la génération.</div>';
    }
    
} else {
    // Afficher les résultats du test
    $site = new Site($db);
    $site->fetch($site_id);
    
    print '<br><h3>Test pour le site : '.$site->nom.'</h3>';
    
    if ($action != 'test_generation') {
        print '<div class="center">';
        print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_generation&site_id='.$site_id.'&token='.newToken().'">Lancer le test de génération</a>';
        print '</div>';
    } else {
        if (!empty($results)) {
            print '<table class="border centpercent">';
            print '<tr class="liste_titre">';
            print '<th>Type de document</th>';
            print '<th>Statut</th>';
            print '<th>Taille</th>';
            print '<th>Détails</th>';
            print '<th>Action</th>';
            print '</tr>';
            
            foreach ($results as $result) {
                print '<tr class="oddeven">';
                print '<td><strong>'.$result['type'].'</strong></td>';
                print '<td>';
                if ($result['status'] == 'OK') {
                    print '<span class="badge badge-status4 badge-status">OK</span>';
                } else {
                    print '<span class="badge badge-status8 badge-status">ERREUR</span>';
                }
                print '</td>';
                print '<td>';
                if ($result['size'] > 0) {
                    print dol_print_size($result['size']);
                } else {
                    print '-';
                }
                print '</td>';
                print '<td>'.$result['details'].'</td>';
                print '<td>';
                if ($result['status'] == 'OK' && $result['file']) {
                    print '<a href="'.DOL_URL_ROOT.'/document.php?modulepart=rendezvousclient&file='.urlencode(str_replace(DOL_DATA_ROOT.'/rendezvousclient/', '', $result['file'])).'" target="_blank">Télécharger</a>';
                }
                print '</td>';
                print '</tr>';
            }
            
            print '</table>';
        }
    }
    
    print '<br><div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
    print '</div>';
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/admin/templates.php">Gestion des templates</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
