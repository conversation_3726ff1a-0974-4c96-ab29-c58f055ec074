<?php
/**
 *	\file       rendezvousclient/install_dolibarr_activation.php
 *	\ingroup    rendezvousclient
 *	\brief      Installation du système d'activation automatique Dolibarr
 */

require_once '../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'install') {
    $results = array();
    
    // 1. Ajouter la colonne dolibarr_module_name si elle n'existe pas
    $sql = "SHOW COLUMNS FROM ".MAIN_DB_PREFIX."avimm_constante_module LIKE 'dolibarr_module_name'";
    $resql = $db->query($sql);
    
    if (!$resql || $db->num_rows($resql) == 0) {
        $sql = "ALTER TABLE ".MAIN_DB_PREFIX."avimm_constante_module 
                ADD COLUMN dolibarr_module_name VARCHAR(100) DEFAULT NULL COMMENT 'Nom du module Dolibarr correspondant'";
        
        if ($db->query($sql)) {
            $results[] = array('action' => 'Ajout colonne dolibarr_module_name', 'status' => 'OK');
        } else {
            $results[] = array('action' => 'Ajout colonne dolibarr_module_name', 'status' => 'ERREUR: ' . $db->lasterror());
        }
    } else {
        $results[] = array('action' => 'Colonne dolibarr_module_name', 'status' => 'EXISTE DÉJÀ');
    }
    
    // 2. Insérer les modules de base
    $modules_base = array(
        array('libelle' => 'Gestion Commerciale', 'dolibarr_module_name' => 'SOCIETE', 'fk_logiciel' => 1),
        array('libelle' => 'Facturation', 'dolibarr_module_name' => 'FACTURE', 'fk_logiciel' => 1),
        array('libelle' => 'Gestion de Stock', 'dolibarr_module_name' => 'STOCK', 'fk_logiciel' => 1),
        array('libelle' => 'CRM', 'dolibarr_module_name' => 'SOCIETE', 'fk_logiciel' => 1),
        array('libelle' => 'Projets', 'dolibarr_module_name' => 'PROJET', 'fk_logiciel' => 1),
        array('libelle' => 'Commandes', 'dolibarr_module_name' => 'COMMANDE', 'fk_logiciel' => 1),
        array('libelle' => 'Expéditions', 'dolibarr_module_name' => 'EXPEDITION', 'fk_logiciel' => 1),
        array('libelle' => 'Contrats', 'dolibarr_module_name' => 'CONTRAT', 'fk_logiciel' => 1),
        array('libelle' => 'Agenda', 'dolibarr_module_name' => 'AGENDA', 'fk_logiciel' => 1),
        array('libelle' => 'Comptabilité', 'dolibarr_module_name' => 'COMPTABILITE', 'fk_logiciel' => 1),
        array('libelle' => 'RH / Adhérents', 'dolibarr_module_name' => 'ADHERENT', 'fk_logiciel' => 2),
        array('libelle' => 'E-commerce', 'dolibarr_module_name' => 'BOUTIQUE', 'fk_logiciel' => 2)
    );
    
    foreach ($modules_base as $module) {
        // Vérifier si le module existe déjà
        $sql = "SELECT rowid FROM ".MAIN_DB_PREFIX."avimm_constante_module WHERE libelle = '".$db->escape($module['libelle'])."'";
        $resql = $db->query($sql);
        
        if ($resql && $db->num_rows($resql) > 0) {
            // Mettre à jour le nom Dolibarr
            $obj = $db->fetch_object($resql);
            $sql = "UPDATE ".MAIN_DB_PREFIX."avimm_constante_module 
                    SET dolibarr_module_name = '".$db->escape($module['dolibarr_module_name'])."' 
                    WHERE rowid = ".$obj->rowid;
            
            if ($db->query($sql)) {
                $results[] = array('action' => 'Mise à jour module '.$module['libelle'], 'status' => 'OK');
            }
        } else {
            // Insérer le nouveau module
            $sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_module (libelle, dolibarr_module_name, fk_logiciel) 
                    VALUES ('".$db->escape($module['libelle'])."', '".$db->escape($module['dolibarr_module_name'])."', ".$module['fk_logiciel'].")";
            
            if ($db->query($sql)) {
                $results[] = array('action' => 'Création module '.$module['libelle'], 'status' => 'OK');
            } else {
                $results[] = array('action' => 'Création module '.$module['libelle'], 'status' => 'ERREUR: ' . $db->lasterror());
            }
        }
    }
    
    // 3. Insérer les constantes de base
    $constantes_base = array(
        array('libelle' => 'MAIN_MODULE_SOCIETE', 'description' => 'Active le module Tiers/Sociétés'),
        array('libelle' => 'MAIN_MODULE_FACTURE', 'description' => 'Active le module Facturation'),
        array('libelle' => 'MAIN_MODULE_STOCK', 'description' => 'Active le module Gestion de Stock'),
        array('libelle' => 'MAIN_MODULE_PROJET', 'description' => 'Active le module Projets'),
        array('libelle' => 'MAIN_MODULE_COMMANDE', 'description' => 'Active le module Commandes'),
        array('libelle' => 'MAIN_MODULE_EXPEDITION', 'description' => 'Active le module Expéditions'),
        array('libelle' => 'MAIN_MODULE_CONTRAT', 'description' => 'Active le module Contrats'),
        array('libelle' => 'MAIN_MODULE_AGENDA', 'description' => 'Active le module Agenda'),
        array('libelle' => 'MAIN_MODULE_COMPTABILITE', 'description' => 'Active le module Comptabilité'),
        array('libelle' => 'MAIN_MODULE_ADHERENT', 'description' => 'Active le module Adhérents'),
        array('libelle' => 'MAIN_MODULE_BOUTIQUE', 'description' => 'Active le module Boutique en ligne'),
        array('libelle' => 'SOCIETE_CODECLIENT_ADDON', 'description' => 'Générateur de code client'),
        array('libelle' => 'FACTURE_ADDON', 'description' => 'Générateur de numérotation des factures'),
        array('libelle' => 'STOCK_CALCULATE_ON_BILL', 'description' => 'Calcul automatique du stock'),
        array('libelle' => 'PROJET_ADDON', 'description' => 'Générateur de référence projet'),
        array('libelle' => 'MAIN_LANG_DEFAULT', 'description' => 'Langue par défaut'),
        array('libelle' => 'MAIN_SIZE_LISTE_LIMIT', 'description' => 'Nombre d\'éléments par page'),
        array('libelle' => 'MAIN_INFO_SOCIETE_NOM', 'description' => 'Nom de la société'),
        array('libelle' => 'MAIN_INFO_SOCIETE_ADRESSE', 'description' => 'Adresse de la société'),
        array('libelle' => 'MAIN_INFO_SOCIETE_CP', 'description' => 'Code postal de la société'),
        array('libelle' => 'MAIN_INFO_SOCIETE_VILLE', 'description' => 'Ville de la société'),
        array('libelle' => 'MAIN_INFO_SOCIETE_TEL', 'description' => 'Téléphone de la société'),
        array('libelle' => 'MAIN_INFO_SOCIETE_MAIL', 'description' => 'Email de la société'),
        array('libelle' => 'MAIN_INFO_SOCIETE_WEB', 'description' => 'Site web de la société')
    );
    
    foreach ($constantes_base as $constante) {
        // Vérifier si la constante existe déjà
        $sql = "SELECT rowid FROM ".MAIN_DB_PREFIX."avimm_constante WHERE libelle = '".$db->escape($constante['libelle'])."'";
        $resql = $db->query($sql);
        
        if (!$resql || $db->num_rows($resql) == 0) {
            // Insérer la nouvelle constante
            $sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante (libelle, description) 
                    VALUES ('".$db->escape($constante['libelle'])."', '".$db->escape($constante['description'])."')";
            
            if ($db->query($sql)) {
                $results[] = array('action' => 'Création constante '.$constante['libelle'], 'status' => 'OK');
            } else {
                $results[] = array('action' => 'Création constante '.$constante['libelle'], 'status' => 'ERREUR: ' . $db->lasterror());
            }
        }
    }
    
    // 4. Créer les liaisons modules-constantes
    $liaisons = array(
        'MAIN_MODULE_SOCIETE' => 'SOCIETE',
        'MAIN_MODULE_FACTURE' => 'FACTURE',
        'MAIN_MODULE_STOCK' => 'STOCK',
        'MAIN_MODULE_PROJET' => 'PROJET',
        'MAIN_MODULE_COMMANDE' => 'COMMANDE',
        'MAIN_MODULE_EXPEDITION' => 'EXPEDITION',
        'MAIN_MODULE_CONTRAT' => 'CONTRAT',
        'MAIN_MODULE_AGENDA' => 'AGENDA',
        'MAIN_MODULE_COMPTABILITE' => 'COMPTABILITE',
        'MAIN_MODULE_ADHERENT' => 'ADHERENT',
        'MAIN_MODULE_BOUTIQUE' => 'BOUTIQUE'
    );
    
    foreach ($liaisons as $constante_name => $module_dolibarr) {
        $sql = "INSERT IGNORE INTO ".MAIN_DB_PREFIX."avimm_constante_inmodule (fk_constante, fk_module) 
                SELECT c.rowid, m.rowid 
                FROM ".MAIN_DB_PREFIX."avimm_constante c, ".MAIN_DB_PREFIX."avimm_constante_module m 
                WHERE c.libelle = '".$db->escape($constante_name)."' AND m.dolibarr_module_name = '".$db->escape($module_dolibarr)."'";
        
        if ($db->query($sql)) {
            if ($db->affected_rows() > 0) {
                $results[] = array('action' => 'Liaison '.$constante_name.' -> '.$module_dolibarr, 'status' => 'OK');
            }
        }
    }
    
    setEventMessages(count($results)." action(s) exécutée(s)", null, 'mesgs');
}

/*
 * View
 */

$title = 'Installation du système d\'activation Dolibarr';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script installe et configure le système d\'activation automatique des modules Dolibarr.';
print '</div>';

// Vérification des prérequis
print '<h3>🔍 Vérification des prérequis</h3>';

$prerequisites = array();

// Tables nécessaires
$required_tables = array('avimm_constante_module', 'avimm_constante', 'avimm_constante_inmodule');
foreach ($required_tables as $table) {
    $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
    $resql = $db->query($sql);
    $exists = ($resql && $db->num_rows($resql) > 0);
    
    $prerequisites[] = array(
        'test' => "Table $table",
        'status' => $exists ? 'OK' : 'MANQUANTE',
        'severity' => $exists ? 'ok' : 'error'
    );
}

// Classe DolibarrManager
$class_exists = class_exists('DolibarrManager');
if (!$class_exists) {
    dol_include_once('/rendezvousclient/core/class/dolibarr_manager.class.php');
    $class_exists = class_exists('DolibarrManager');
}

$prerequisites[] = array(
    'test' => 'Classe DolibarrManager',
    'status' => $class_exists ? 'OK' : 'MANQUANTE',
    'severity' => $class_exists ? 'ok' : 'error'
);

// Colonne dolibarr_module_name
$sql = "SHOW COLUMNS FROM ".MAIN_DB_PREFIX."avimm_constante_module LIKE 'dolibarr_module_name'";
$resql = $db->query($sql);
$column_exists = ($resql && $db->num_rows($resql) > 0);

$prerequisites[] = array(
    'test' => 'Colonne dolibarr_module_name',
    'status' => $column_exists ? 'OK' : 'MANQUANTE',
    'severity' => $column_exists ? 'ok' : 'warning'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Prérequis</th>';
print '<th>Statut</th>';
print '</tr>';

$can_install = true;
foreach ($prerequisites as $prereq) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$prereq['test'].'</strong></td>';
    print '<td>';
    
    switch ($prereq['severity']) {
        case 'ok':
            print '<span class="badge badge-status4 badge-status">'.$prereq['status'].'</span>';
            break;
        case 'warning':
            print '<span class="badge badge-status7 badge-status">'.$prereq['status'].'</span>';
            break;
        case 'error':
            print '<span class="badge badge-status8 badge-status">'.$prereq['status'].'</span>';
            $can_install = false;
            break;
    }
    
    print '</td>';
    print '</tr>';
}

print '</table>';

// Installation
if ($can_install) {
    print '<br><h3>🚀 Installation</h3>';
    
    print '<div class="tabsAction">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=install&token='.newToken().'">Installer/Mettre à jour</a>';
    print '</div>';
} else {
    print '<div class="error">';
    print '<strong>❌ Installation impossible</strong> - Certains prérequis ne sont pas remplis.';
    print '</div>';
}

// Affichage des résultats
if (!empty($results)) {
    print '<br><h3>📋 Résultats de l\'installation</h3>';
    
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Action</th>';
    print '<th>Statut</th>';
    print '</tr>';
    
    $nb_ok = 0;
    $nb_error = 0;
    
    foreach ($results as $result) {
        print '<tr class="oddeven">';
        print '<td><strong>'.$result['action'].'</strong></td>';
        print '<td>';
        if ($result['status'] == 'OK' || strpos($result['status'], 'EXISTE DÉJÀ') !== false) {
            print '<span class="badge badge-status4 badge-status">'.$result['status'].'</span>';
            $nb_ok++;
        } else {
            print '<span class="badge badge-status8 badge-status">'.$result['status'].'</span>';
            $nb_error++;
        }
        print '</td>';
        print '</tr>';
    }
    
    print '</table>';
    
    print '<br>';
    if ($nb_error == 0) {
        print '<div class="ok">';
        print '<strong>✅ Installation réussie !</strong> ('.$nb_ok.' actions exécutées)';
        print '</div>';
    } else {
        print '<div class="warning">';
        print '<strong>⚠️ '.$nb_error.' erreur(s) lors de l\'installation</strong>';
        print '</div>';
    }
}

// Étapes suivantes
print '<br><h3>📋 Étapes suivantes</h3>';

print '<div class="info">';
print '<ol>';
print '<li><strong>Configurer l\'instance cible</strong> - <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/admin/setup_dolibarr_instance.php">Paramètres de l\'instance Dolibarr</a></li>';
print '<li><strong>Tester l\'activation</strong> - <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_dolibarr_activation.php">Test d\'activation automatique</a></li>';
print '<li><strong>Créer une démo</strong> - <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_demo_creation.php">Création de démo complète</a></li>';
print '</ol>';
print '</div>';

// Liens utiles
print '<br><h3>🔗 Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/admin/setup_dolibarr_instance.php">Configurer instance</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_dolibarr_activation.php">Tester activation</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
