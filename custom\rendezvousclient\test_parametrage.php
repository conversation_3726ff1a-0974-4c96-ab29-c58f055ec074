<?php
/**
 *	\file       rendezvousclient/test_parametrage.php
 *	\ingroup    rendezvousclient
 *	\brief      Test du fichier parametrage.php
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

dol_include_once('/rendezvousclient/site/class/site.class.php');

$action = GETPOST('action', 'aZ09');

/*
 * View
 */

$title = 'Test du fichier parametrage.php';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce test vérifie que le fichier parametrage.php fonctionne correctement après les corrections.';
print '</div>';

// 1. Vérifier les variables corrigées
print '<h3>1. Variables corrigées</h3>';

$variables_fixes = array(
    '$help_url' => 'Variable pour l\'URL d\'aide',
    '$contactid' => 'Variable pour l\'ID du contact'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Variable</th>';
print '<th>Description</th>';
print '<th>Statut</th>';
print '</tr>';

foreach ($variables_fixes as $var => $desc) {
    print '<tr class="oddeven">';
    print '<td><code>'.$var.'</code></td>';
    print '<td>'.$desc.'</td>';
    print '<td><span class="badge badge-status4 badge-status">CORRIGÉ</span></td>';
    print '</tr>';
}

print '</table>';

// 2. Vérifier les corrections d'array
print '<br><h3>2. Corrections d\'accès aux arrays</h3>';

$array_fixes = array(
    'modules[X][\'checked\']' => 'Vérification avec isset() avant accès',
    'arrayconstante[X]' => 'Vérification avec isset() avant foreach',
    'modules[X][\'extrafields\']' => 'Vérification avec isset() avant accès',
    'modules[X][\'devspe\']' => 'Vérification avec isset() avant accès',
    'modules[X][\'param\']' => 'Vérification avec isset() avant accès'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Array</th>';
print '<th>Correction appliquée</th>';
print '<th>Statut</th>';
print '</tr>';

foreach ($array_fixes as $array => $correction) {
    print '<tr class="oddeven">';
    print '<td><code>'.$array.'</code></td>';
    print '<td>'.$correction.'</td>';
    print '<td><span class="badge badge-status4 badge-status">CORRIGÉ</span></td>';
    print '</tr>';
}

print '</table>';

// 3. Tester l'accès au fichier parametrage
print '<br><h3>3. Test d\'accès au fichier parametrage</h3>';

$sql = "SELECT rowid, nom FROM ".MAIN_DB_PREFIX."rendez_vous_site ORDER BY rowid DESC LIMIT 5";
$resql = $db->query($sql);

if ($resql && $db->num_rows($resql) > 0) {
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>ID</th>';
    print '<th>Site</th>';
    print '<th>Test parametrage</th>';
    print '</tr>';
    
    while ($obj = $db->fetch_object($resql)) {
        print '<tr class="oddeven">';
        print '<td>'.$obj->rowid.'</td>';
        print '<td><strong>'.$obj->nom.'</strong></td>';
        print '<td>';
        print '<a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/parametrage.php?id='.$obj->rowid.'" target="_blank" class="butAction">Tester parametrage</a>';
        print '</td>';
        print '</tr>';
    }
    
    print '</table>';
} else {
    print '<div class="warning">Aucun site trouvé pour tester. <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créez un site</a> d\'abord.</div>';
}

// 4. Vérifier les tables nécessaires
print '<br><h3>4. Tables nécessaires pour le parametrage</h3>';

$tables_parametrage = array(
    'rendez_vous_site' => 'Sites',
    'rendez_vous_site_module' => 'Modules des sites',
    'rendez_vous_site_module_devspe' => 'Développements spécifiques',
    'rendez_vous_site_module_param' => 'Paramètres des modules',
    'rendez_vous_site_module_extrafields' => 'Champs supplémentaires',
    'avimm_constante_module' => 'Modules disponibles',
    'avimm_constante' => 'Constantes disponibles',
    'avimm_constante_inmodule' => 'Liaison constantes-modules'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Table</th>';
print '<th>Description</th>';
print '<th>Existe</th>';
print '<th>Enregistrements</th>';
print '</tr>';

foreach ($tables_parametrage as $table => $description) {
    $full_table = MAIN_DB_PREFIX . $table;
    
    // Vérifier si la table existe
    $sql = "SHOW TABLES LIKE '".$full_table."'";
    $resql = $db->query($sql);
    $exists = ($resql && $db->num_rows($resql) > 0);
    
    $count = 0;
    if ($exists) {
        $sql = "SELECT COUNT(*) as nb FROM ".$full_table;
        $resql = $db->query($sql);
        if ($resql) {
            $obj = $db->fetch_object($resql);
            $count = $obj->nb;
        }
    }
    
    print '<tr class="oddeven">';
    print '<td><strong>'.$table.'</strong></td>';
    print '<td>'.$description.'</td>';
    print '<td>';
    if ($exists) {
        print '<span class="badge badge-status4 badge-status">OUI</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">NON</span>';
    }
    print '</td>';
    print '<td>'.$count.'</td>';
    print '</tr>';
}

print '</table>';

// 5. Exemple de données pour tester
print '<br><h3>5. Données d\'exemple pour tester</h3>';

if ($action == 'create_test_data') {
    // Créer des données de test si nécessaire
    $test_results = array();
    
    // Vérifier s'il y a des modules
    $sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."avimm_constante_module";
    $resql = $db->query($sql);
    $nb_modules = 0;
    if ($resql) {
        $obj = $db->fetch_object($resql);
        $nb_modules = $obj->nb;
    }
    
    if ($nb_modules == 0) {
        // Créer quelques modules de test
        $modules_test = array(
            array('libelle' => 'Module Test 1', 'fk_logiciel' => 1),
            array('libelle' => 'Module Test 2', 'fk_logiciel' => 1),
            array('libelle' => 'Module Test 3', 'fk_logiciel' => 2)
        );
        
        foreach ($modules_test as $module) {
            $sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante_module (libelle, fk_logiciel) VALUES ('".$db->escape($module['libelle'])."', ".$module['fk_logiciel'].")";
            if ($db->query($sql)) {
                $test_results[] = array('action' => 'Création module '.$module['libelle'], 'status' => 'OK');
            } else {
                $test_results[] = array('action' => 'Création module '.$module['libelle'], 'status' => 'ERREUR');
            }
        }
    }
    
    // Vérifier s'il y a des constantes
    $sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."avimm_constante";
    $resql = $db->query($sql);
    $nb_constantes = 0;
    if ($resql) {
        $obj = $db->fetch_object($resql);
        $nb_constantes = $obj->nb;
    }
    
    if ($nb_constantes == 0) {
        // Créer quelques constantes de test
        $constantes_test = array(
            array('libelle' => 'Constante Test 1', 'description' => 'Description de la constante 1'),
            array('libelle' => 'Constante Test 2', 'description' => 'Description de la constante 2'),
            array('libelle' => 'Constante Test 3', 'description' => 'Description de la constante 3')
        );
        
        foreach ($constantes_test as $constante) {
            $sql = "INSERT INTO ".MAIN_DB_PREFIX."avimm_constante (libelle, description) VALUES ('".$db->escape($constante['libelle'])."', '".$db->escape($constante['description'])."')";
            if ($db->query($sql)) {
                $test_results[] = array('action' => 'Création constante '.$constante['libelle'], 'status' => 'OK');
            } else {
                $test_results[] = array('action' => 'Création constante '.$constante['libelle'], 'status' => 'ERREUR');
            }
        }
    }
    
    if (!empty($test_results)) {
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Action</th>';
        print '<th>Statut</th>';
        print '</tr>';
        
        foreach ($test_results as $result) {
            print '<tr class="oddeven">';
            print '<td>'.$result['action'].'</td>';
            print '<td>';
            if ($result['status'] == 'OK') {
                print '<span class="badge badge-status4 badge-status">OK</span>';
            } else {
                print '<span class="badge badge-status8 badge-status">ERREUR</span>';
            }
            print '</td>';
            print '</tr>';
        }
        
        print '</table>';
        
        setEventMessages("Données de test créées", null, 'mesgs');
    } else {
        print '<div class="info">Aucune donnée de test à créer</div>';
    }
} else {
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=create_test_data&token='.newToken().'">Créer des données de test</a>';
    print '</div>';
    
    print '<div class="info">';
    print 'Si vous n\'avez pas de modules ou constantes, créez des données de test pour pouvoir tester le parametrage.';
    print '</div>';
}

// Résumé des corrections
print '<br><h3>Résumé des corrections appliquées</h3>';
print '<div class="ok">';
print '<ul>';
print '<li><strong>Variables manquantes :</strong> $help_url et $contactid ajoutées</li>';
print '<li><strong>Accès aux arrays :</strong> Vérifications isset() ajoutées partout</li>';
print '<li><strong>Modules :</strong> Vérification avant accès à [\'checked\']</li>';
print '<li><strong>Constantes :</strong> Vérification avant foreach sur arrayconstante</li>';
print '<li><strong>Extrafields :</strong> Vérification avant accès à [\'extrafields\']</li>';
print '<li><strong>Dev spé :</strong> Vérification avant accès à [\'devspe\']</li>';
print '<li><strong>Paramètres :</strong> Vérification avant accès à [\'param\']</li>';
print '</ul>';
print '</div>';

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créer un site</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
