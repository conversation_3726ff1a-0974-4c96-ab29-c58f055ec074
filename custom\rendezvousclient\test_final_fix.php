<?php
/**
 *	\file       rendezvousclient/test_final_fix.php
 *	\ingroup    rendezvousclient
 *	\brief      Test final de toutes les corrections
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'test_all_fixes') {
    $results = array();
    
    // Test 1: Vérifier que $user->projet existe
    $results[] = array(
        'test' => 'Propriété $user->projet',
        'status' => isset($user->projet) ? 'OK' : 'ERREUR',
        'details' => isset($user->projet) ? 'Propriété définie' : 'Proprié<PERSON> manquante'
    );
    
    // Test 2: Vérifier les droits du module
    $results[] = array(
        'test' => 'Droits module rendezvousclient',
        'status' => isset($user->rights->rendezvousclient) ? 'OK' : 'ERREUR',
        'details' => isset($user->rights->rendezvousclient) ? 'Droits définis' : 'Droits manquants'
    );
    
    // Test 3: Vérifier les tables principales
    $tables_to_check = array(
        'rendez_vous' => 'Table principale des rendez-vous',
        'rendez_vous_site' => 'Sites clients',
        'avimm_constante_logiciel' => 'Logiciels disponibles'
    );
    
    foreach ($tables_to_check as $table => $description) {
        $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
        $resql = $db->query($sql);
        
        $results[] = array(
            'test' => 'Table '.$table,
            'status' => ($resql && $db->num_rows($resql) > 0) ? 'OK' : 'ERREUR',
            'details' => ($resql && $db->num_rows($resql) > 0) ? 'Table existe' : 'Table manquante'
        );
    }
    
    // Test 4: Vérifier les classes principales
    $classes_to_check = array(
        'Rendezvous' => 'Classe des rendez-vous',
        'Site' => 'Classe des sites',
        'SyntheseCDC' => 'Classe synthèse CDC'
    );
    
    foreach ($classes_to_check as $class => $description) {
        $results[] = array(
            'test' => 'Classe '.$class,
            'status' => class_exists($class) ? 'OK' : 'ERREUR',
            'details' => class_exists($class) ? 'Classe chargée' : 'Classe non trouvée'
        );
    }
    
    // Test 5: Test de création d'objets
    try {
        dol_include_once('/rendezvousclient/rdv/class/rendezvous.class.php');
        $rdv_test = new Rendezvous($db);
        $results[] = array(
            'test' => 'Instanciation Rendezvous',
            'status' => 'OK',
            'details' => 'Objet créé avec succès'
        );
    } catch (Exception $e) {
        $results[] = array(
            'test' => 'Instanciation Rendezvous',
            'status' => 'ERREUR',
            'details' => 'Erreur: '.$e->getMessage()
        );
    }
    
    try {
        dol_include_once('/rendezvousclient/site/class/site.class.php');
        $site_test = new Site($db);
        $results[] = array(
            'test' => 'Instanciation Site',
            'status' => 'OK',
            'details' => 'Objet créé avec succès'
        );
    } catch (Exception $e) {
        $results[] = array(
            'test' => 'Instanciation Site',
            'status' => 'ERREUR',
            'details' => 'Erreur: '.$e->getMessage()
        );
    }
}

/*
 * View
 */

$title = 'Test final des corrections';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce test vérifie que toutes les corrections ont été appliquées avec succès et que le module fonctionne correctement.';
print '</div>';

if ($action != 'test_all_fixes') {
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_all_fixes&token='.newToken().'">Lancer tous les tests</a>';
    print '</div>';
    
    print '<br><h3>Corrections appliquées</h3>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Type de correction</th>';
    print '<th>Fichiers concernés</th>';
    print '<th>Description</th>';
    print '</tr>';
    
    $corrections = array(
        array(
            'type' => 'Propriétés utilisateur',
            'files' => 'Tous les fichiers principaux',
            'description' => 'Ajout de $user->projet = new stdClass() pour éviter les erreurs dans security.lib.php'
        ),
        array(
            'type' => 'Variables manquantes',
            'files' => 'card.php, list.php',
            'description' => 'Ajout de $backtopage, $help_url, $lastkey avec valeurs par défaut'
        ),
        array(
            'type' => 'Vérifications de tableaux',
            'files' => 'list.php (tous)',
            'description' => 'Ajout de !empty() pour toutes les vérifications $arrayfields'
        ),
        array(
            'type' => 'Requêtes SQL',
            'files' => 'card.php (tous)',
            'description' => 'Correction des requêtes avec fetch_object() au lieu de foreach'
        ),
        array(
            'type' => 'Méthodes de classe',
            'files' => 'site.class.php',
            'description' => 'Ajout des méthodes create(), update(), createUtilisateur(), deleteAllUtilisateurs()'
        ),
        array(
            'type' => 'Tables de base',
            'files' => 'SQL',
            'description' => 'Création de toutes les tables manquantes avec données de base'
        )
    );
    
    foreach ($corrections as $correction) {
        print '<tr class="oddeven">';
        print '<td><strong>'.$correction['type'].'</strong></td>';
        print '<td>'.$correction['files'].'</td>';
        print '<td>'.$correction['description'].'</td>';
        print '</tr>';
    }
    
    print '</table>';
    
} else {
    print '<h3>Résultats des tests</h3>';
    
    if (!empty($results)) {
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Test</th>';
        print '<th>Statut</th>';
        print '<th>Détails</th>';
        print '</tr>';
        
        $nb_ok = 0;
        $nb_error = 0;
        
        foreach ($results as $result) {
            print '<tr class="oddeven">';
            print '<td><strong>'.$result['test'].'</strong></td>';
            print '<td>';
            if ($result['status'] == 'OK') {
                print '<span class="badge badge-status4 badge-status">OK</span>';
                $nb_ok++;
            } else {
                print '<span class="badge badge-status8 badge-status">ERREUR</span>';
                $nb_error++;
            }
            print '</td>';
            print '<td>'.$result['details'].'</td>';
            print '</tr>';
        }
        
        print '</table>';
        
        print '<br>';
        if ($nb_error == 0) {
            print '<div class="ok">';
            print '<strong>Excellent !</strong> Tous les tests sont passés avec succès ('.$nb_ok.' tests OK).';
            print '<br>Le module est maintenant entièrement fonctionnel sans erreurs PHP.';
            print '</div>';
            
            print '<br><h3>Module prêt à utiliser</h3>';
            print '<div class="tabsAction">';
            print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rdv/list.php">Liste des rendez-vous</a>';
            print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rdv/card.php?action=create">Créer un rendez-vous</a>';
            print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
            print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créer un site</a>';
            print '</div>';
            
        } else {
            print '<div class="warning">';
            print '<strong>Attention !</strong> '.$nb_error.' test(s) en erreur sur '.($nb_ok + $nb_error).' tests.';
            print '<br>Veuillez corriger les erreurs restantes avant d\'utiliser le module.';
            print '</div>';
        }
    }
    
    print '<div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
    print '</div>';
}

// Informations système
print '<br><h3>Informations système</h3>';
print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Information</th>';
print '<th>Valeur</th>';
print '</tr>';

$system_info = array(
    'Version PHP' => phpversion(),
    'Version Dolibarr' => DOL_VERSION,
    'Module activé' => (!empty($conf->rendezvousclient->enabled) ? 'Oui' : 'Non'),
    'Utilisateur admin' => ($user->admin ? 'Oui' : 'Non'),
    'ID utilisateur' => $user->id,
    'Propriété $user->projet' => (isset($user->projet) ? 'Définie' : 'Non définie'),
    'Droits module' => (isset($user->rights->rendezvousclient) ? 'Définis' : 'Non définis')
);

foreach ($system_info as $info => $value) {
    print '<tr class="oddeven">';
    print '<td>'.$info.'</td>';
    print '<td><strong>'.$value.'</strong></td>';
    print '</tr>';
}

print '</table>';

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/install_missing_tables.php">Installer les tables</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_forms.php">Test des formulaires</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
