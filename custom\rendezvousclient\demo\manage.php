<?php
/**
 *	\file       rendezvousclient/demo/manage.php
 *	\ingroup    rendezvousclient
 *	\brief      Gestion avancée des démos créées
 */

require_once '../../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
dol_include_once('/rendezvousclient/lib/rendezvousclient.lib.php');

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

// Variables
$help_url = '';
$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'delete_demo') {
    $demo_name = GETPOST('demo_name', 'alpha');
    
    if ($demo_name) {
        $demos_dir = DOL_DATA_ROOT . '/demos/';
        $demo_path = $demos_dir . $demo_name;
        
        if (is_dir($demo_path)) {
            // Supprimer récursivement le répertoire de la démo
            function deleteDirectory($dir) {
                if (!is_dir($dir)) return false;
                $files = array_diff(scandir($dir), array('.', '..'));
                foreach ($files as $file) {
                    $path = $dir . '/' . $file;
                    is_dir($path) ? deleteDirectory($path) : unlink($path);
                }
                return rmdir($dir);
            }
            
            if (deleteDirectory($demo_path)) {
                // Supprimer aussi de la base de données
                $sql = "DELETE FROM ".MAIN_DB_PREFIX."rendez_vous_demo WHERE nom = '".$db->escape($demo_name)."'";
                $db->query($sql);
                
                setEventMessages("Démo supprimée avec succès", null, 'mesgs');
            } else {
                setEventMessages("Erreur lors de la suppression de la démo", null, 'errors');
            }
        }
    }
}

if ($action == 'clean_orphans') {
    $cleaned = 0;
    $demos_dir = DOL_DATA_ROOT . '/demos/';
    
    if (is_dir($demos_dir)) {
        $demo_folders = array_diff(scandir($demos_dir), array('.', '..'));
        
        foreach ($demo_folders as $folder) {
            $folder_path = $demos_dir . $folder;
            if (is_dir($folder_path)) {
                // Vérifier si la démo est en base
                $sql_check = "SELECT rowid FROM ".MAIN_DB_PREFIX."rendez_vous_demo WHERE nom = '".$db->escape($folder)."'";
                $resql_check = $db->query($sql_check);
                
                if (!$resql_check || $db->num_rows($resql_check) == 0) {
                    // Démo orpheline, la supprimer
                    function deleteDirectory($dir) {
                        if (!is_dir($dir)) return false;
                        $files = array_diff(scandir($dir), array('.', '..'));
                        foreach ($files as $file) {
                            $path = $dir . '/' . $file;
                            is_dir($path) ? deleteDirectory($path) : unlink($path);
                        }
                        return rmdir($dir);
                    }
                    
                    if (deleteDirectory($folder_path)) {
                        $cleaned++;
                    }
                }
            }
        }
    }
    
    setEventMessages("$cleaned démo(s) orpheline(s) supprimée(s)", null, 'mesgs');
}

/*
 * View
 */

$form = new Form($db);

$title = 'Gestion des démos Dolibarr';
llxHeader('', $title, $help_url);

print load_fiche_titre($title, '', 'generic');

print '<div class="info">';
print 'Cette page permet de gérer toutes les démos créées à partir des configurations de sites.';
print '</div>';

// Statistiques globales
print '<h3>📊 Statistiques</h3>';

$sql_stats = "SELECT 
                COUNT(*) as total_demos,
                COUNT(CASE WHEN date_creation > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as demos_semaine,
                COUNT(CASE WHEN date_creation > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as demos_mois,
                AVG(CHAR_LENGTH(modules_selectionnes) - CHAR_LENGTH(REPLACE(modules_selectionnes, ',', '')) + 1) as avg_modules
              FROM ".MAIN_DB_PREFIX."rendez_vous_demo";

$resql_stats = $db->query($sql_stats);
if ($resql_stats) {
    $stats = $db->fetch_object($resql_stats);
    
    print '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">';
    
    print '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #667eea;">';
    print '<div style="font-size: 2em; font-weight: bold; color: #667eea;">'.$stats->total_demos.'</div>';
    print '<div>Total des démos</div>';
    print '</div>';
    
    print '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #28a745;">';
    print '<div style="font-size: 2em; font-weight: bold; color: #28a745;">'.$stats->demos_semaine.'</div>';
    print '<div>Cette semaine</div>';
    print '</div>';
    
    print '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #ffc107;">';
    print '<div style="font-size: 2em; font-weight: bold; color: #ffc107;">'.$stats->demos_mois.'</div>';
    print '<div>Ce mois</div>';
    print '</div>';
    
    print '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #17a2b8;">';
    print '<div style="font-size: 2em; font-weight: bold; color: #17a2b8;">'.round($stats->avg_modules, 1).'</div>';
    print '<div>Modules moy./démo</div>';
    print '</div>';
    
    print '</div>';
}

// Actions de maintenance
print '<h3>🔧 Actions de maintenance</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=clean_orphans&token='.newToken().'">Nettoyer les démos orphelines</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_demo_creation.php">Créer une nouvelle démo</a>';
print '</div>';

// Liste des démos en base de données
print '<h3>💾 Démos en base de données</h3>';

$sql = "SELECT d.rowid, d.fk_projet, d.nom, d.url, d.modules_selectionnes, d.date_creation,
               p.ref as projet_ref, p.title as projet_title,
               s.nom as site_nom
        FROM ".MAIN_DB_PREFIX."rendez_vous_demo d
        LEFT JOIN ".MAIN_DB_PREFIX."projet p ON d.fk_projet = p.rowid
        LEFT JOIN ".MAIN_DB_PREFIX."rendez_vous_site s ON s.fk_projet = d.fk_projet
        ORDER BY d.date_creation DESC";

$resql = $db->query($sql);

if ($resql) {
    $num = $db->num_rows($resql);
    
    if ($num > 0) {
        print '<div class="div-table-responsive">';
        print '<table class="tagtable liste listwithfilterbefore centpercent">';
        
        // En-têtes
        print '<tr class="liste_titre">';
        print '<th>Nom de la démo</th>';
        print '<th>Site/Projet</th>';
        print '<th>Modules</th>';
        print '<th>Date de création</th>';
        print '<th>Statut fichier</th>';
        print '<th>Actions</th>';
        print '</tr>';
        
        $i = 0;
        while ($obj = $db->fetch_object($resql)) {
            print '<tr class="oddeven">';
            
            // Nom de la démo
            print '<td><strong>'.$obj->nom.'</strong></td>';
            
            // Site/Projet
            print '<td>';
            if ($obj->site_nom) {
                print '<strong>'.$obj->site_nom.'</strong><br>';
            }
            if ($obj->projet_ref) {
                print '<small>'.$obj->projet_ref;
                if ($obj->projet_title) {
                    print ' - '.$obj->projet_title;
                }
                print '</small>';
            } else {
                print '<em>Aucun projet</em>';
            }
            print '</td>';
            
            // Modules
            print '<td>';
            if ($obj->modules_selectionnes) {
                $modules = explode(',', $obj->modules_selectionnes);
                print '<span class="badge badge-info">'.count($modules).' module(s)</span>';
                if (count($modules) <= 3) {
                    print '<br><small>'.implode(', ', $modules).'</small>';
                } else {
                    print '<br><small>'.implode(', ', array_slice($modules, 0, 2)).' et '.(count($modules)-2).' autres</small>';
                }
            } else {
                print '<em>Aucun module</em>';
            }
            print '</td>';
            
            // Date de création
            print '<td>'.dol_print_date($db->jdate($obj->date_creation), 'dayhour').'</td>';
            
            // Statut fichier
            print '<td>';
            $demos_dir = DOL_DATA_ROOT . '/demos/';
            $demo_path = $demos_dir . $obj->nom;
            if (is_dir($demo_path) && file_exists($demo_path . '/index.php')) {
                print '<span class="badge badge-status4 badge-status">✅ ACTIVE</span>';
            } else {
                print '<span class="badge badge-status8 badge-status">❌ MANQUANT</span>';
            }
            print '</td>';
            
            // Actions
            print '<td>';
            if (is_dir($demo_path) && file_exists($demo_path . '/index.php')) {
                print '<a href="'.$obj->url.'" target="_blank" class="butAction" title="Voir la démo">👁️ Voir</a> ';
            }
            print '<a href="'.$_SERVER['PHP_SELF'].'?action=delete_demo&demo_name='.$obj->nom.'&token='.newToken().'" class="butActionDelete" onclick="return confirm(\'Êtes-vous sûr de vouloir supprimer cette démo ?\');" title="Supprimer">🗑️ Suppr.</a>';
            print '</td>';
            
            print '</tr>';
            $i++;
        }
        
        print '</table>';
        print '</div>';
    } else {
        print '<div class="info">Aucune démo en base de données</div>';
    }
} else {
    dol_print_error($db);
}

// Démos dans le répertoire (fichiers)
print '<br><h3>📁 Démos dans le répertoire</h3>';

$demos_dir = DOL_DATA_ROOT . '/demos/';
if (is_dir($demos_dir)) {
    $demo_folders = array_diff(scandir($demos_dir), array('.', '..'));
    
    if (!empty($demo_folders)) {
        print '<div class="div-table-responsive">';
        print '<table class="tagtable liste centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Nom du répertoire</th>';
        print '<th>Taille</th>';
        print '<th>Date de modification</th>';
        print '<th>En base</th>';
        print '<th>Actions</th>';
        print '</tr>';
        
        foreach ($demo_folders as $folder) {
            $folder_path = $demos_dir . $folder;
            if (is_dir($folder_path)) {
                // Vérifier si la démo est en base
                $sql_check = "SELECT rowid FROM ".MAIN_DB_PREFIX."rendez_vous_demo WHERE nom = '".$db->escape($folder)."'";
                $resql_check = $db->query($sql_check);
                $in_db = ($resql_check && $db->num_rows($resql_check) > 0);
                
                print '<tr class="oddeven">';
                print '<td><strong>'.$folder.'</strong></td>';
                
                // Taille du répertoire
                $size = 0;
                try {
                    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($folder_path));
                    foreach ($iterator as $file) {
                        if ($file->isFile()) {
                            $size += $file->getSize();
                        }
                    }
                } catch (Exception $e) {
                    // Ignorer les erreurs de lecture
                }
                print '<td>'.dol_print_size($size).'</td>';
                
                // Date de modification
                print '<td>'.date('d/m/Y H:i', filemtime($folder_path)).'</td>';
                
                // En base
                print '<td>';
                if ($in_db) {
                    print '<span class="badge badge-status4 badge-status">✅ OUI</span>';
                } else {
                    print '<span class="badge badge-status8 badge-status">❌ NON</span>';
                }
                print '</td>';
                
                // Actions
                print '<td>';
                if (file_exists($folder_path . '/index.php')) {
                    $demo_url = DOL_URL_ROOT . '/document.php?modulepart=demos&file=' . $folder . '/index.php';
                    print '<a href="'.$demo_url.'" target="_blank" class="butAction" title="Voir">👁️ Voir</a> ';
                }
                print '<a href="'.$_SERVER['PHP_SELF'].'?action=delete_demo&demo_name='.$folder.'&token='.newToken().'" class="butActionDelete" onclick="return confirm(\'Supprimer le répertoire '.$folder.' ?\');" title="Supprimer">🗑️ Suppr.</a>';
                print '</td>';
                
                print '</tr>';
            }
        }
        
        print '</table>';
        print '</div>';
    } else {
        print '<div class="info">Aucun répertoire de démo trouvé</div>';
    }
} else {
    print '<div class="warning">Répertoire de démos non trouvé : '.$demos_dir.'</div>';
    print '<div class="center">';
    print '<a class="butAction" href="#" onclick="if(confirm(\'Créer le répertoire de démos ?\')) { window.location.href=\''.DOL_URL_ROOT.'/custom/rendezvousclient/test_demo_creation.php\'; }">Créer le répertoire</a>';
    print '</div>';
}

// Liens utiles
print '<br><h3>🔗 Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_demo_creation.php">Créer une démo</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/demo/list.php">Liste simple</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
