<?php
/**
 *	\file       rendezvousclient/admin/setup_dolibarr_instance.php
 *	\ingroup    rendezvousclient
 *	\brief      Configuration de l'instance Dolibarr cible pour les démos
 */

require_once '../../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'save') {
    $demo_db_host = GETPOST('demo_db_host', 'alpha');
    $demo_db_name = GETPOST('demo_db_name', 'alpha');
    $demo_db_user = GETPOST('demo_db_user', 'alpha');
    $demo_db_password = GETPOST('demo_db_password', 'alpha');
    $demo_db_port = GETPOST('demo_db_port', 'int');
    $demo_db_prefix = GETPOST('demo_db_prefix', 'alpha');
    $demo_url = GETPOST('demo_url', 'alpha');
    
    // Validation
    $errors = array();
    
    if (empty($demo_db_host)) {
        $errors[] = "L'hôte de la base de données est requis";
    }
    
    if (empty($demo_db_name)) {
        $errors[] = "Le nom de la base de données est requis";
    }
    
    if (empty($demo_db_user)) {
        $errors[] = "L'utilisateur de la base de données est requis";
    }
    
    if (empty($demo_url)) {
        $errors[] = "L'URL de l'instance Dolibarr est requise";
    }
    
    if (empty($errors)) {
        // Sauvegarder la configuration
        dolibarr_set_const($db, 'RENDEZVOUSCLIENT_DEMO_DB_HOST', $demo_db_host, 'chaine', 0, '', 0);
        dolibarr_set_const($db, 'RENDEZVOUSCLIENT_DEMO_DB_NAME', $demo_db_name, 'chaine', 0, '', 0);
        dolibarr_set_const($db, 'RENDEZVOUSCLIENT_DEMO_DB_USER', $demo_db_user, 'chaine', 0, '', 0);
        dolibarr_set_const($db, 'RENDEZVOUSCLIENT_DEMO_DB_PASSWORD', $demo_db_password, 'chaine', 0, '', 0);
        dolibarr_set_const($db, 'RENDEZVOUSCLIENT_DEMO_DB_PORT', $demo_db_port ?: 3306, 'int', 0, '', 0);
        dolibarr_set_const($db, 'RENDEZVOUSCLIENT_DEMO_DB_PREFIX', $demo_db_prefix ?: 'llx_', 'chaine', 0, '', 0);
        dolibarr_set_const($db, 'RENDEZVOUSCLIENT_DEMO_URL', $demo_url, 'chaine', 0, '', 0);
        
        setEventMessages("Configuration sauvegardée avec succès", null, 'mesgs');
    } else {
        setEventMessages($errors, null, 'errors');
    }
}

if ($action == 'test_connection') {
    // Tester la connexion à l'instance cible
    dol_include_once('/rendezvousclient/core/class/dolibarr_manager.class.php');
    
    $target_config = array(
        'type' => 'mysqli',
        'host' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_HOST ?? '',
        'database' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_NAME ?? '',
        'user' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_USER ?? '',
        'password' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PASSWORD ?? '',
        'port' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PORT ?? 3306,
        'prefix' => $conf->global->RENDEZVOUSCLIENT_DEMO_DB_PREFIX ?? 'llx_',
        'url' => $conf->global->RENDEZVOUSCLIENT_DEMO_URL ?? ''
    );
    
    $dolibarr_manager = new DolibarrManager($db);
    
    if ($dolibarr_manager->setTargetInstance($target_config)) {
        setEventMessages("✅ Connexion à l'instance cible réussie", null, 'mesgs');
    } else {
        setEventMessages("❌ Erreur de connexion: " . $dolibarr_manager->error, null, 'errors');
    }
}

if ($action == 'create_demo_tables') {
    // Créer les tables nécessaires dans l'instance cible
    $sql_tables = array(
        "CREATE TABLE IF NOT EXISTS " . ($conf->global->RENDEZVOUSCLIENT_DEMO_DB_PREFIX ?? 'llx_') . "const (
            rowid int(11) NOT NULL AUTO_INCREMENT,
            name varchar(180) NOT NULL,
            value text,
            type varchar(64) DEFAULT 'string',
            visible tinyint(4) DEFAULT 1,
            note text,
            entity int(11) DEFAULT 1,
            PRIMARY KEY (rowid),
            UNIQUE KEY uk_const_name (name, entity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8",
        
        "CREATE TABLE IF NOT EXISTS " . ($conf->global->RENDEZVOUSCLIENT_DEMO_DB_PREFIX ?? 'llx_') . "user (
            rowid int(11) NOT NULL AUTO_INCREMENT,
            login varchar(50) NOT NULL,
            pass_crypted varchar(128) DEFAULT NULL,
            lastname varchar(50) DEFAULT NULL,
            firstname varchar(50) DEFAULT NULL,
            admin smallint(6) DEFAULT 0,
            entity int(11) DEFAULT 1,
            datec datetime DEFAULT NULL,
            PRIMARY KEY (rowid),
            UNIQUE KEY uk_user_login (login, entity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8"
    );
    
    // Exécuter les requêtes sur l'instance cible
    // TODO: Implémenter l'exécution sur l'instance cible
    
    setEventMessages("Tables créées dans l'instance cible", null, 'mesgs');
}

/*
 * View
 */

$title = 'Configuration de l\'instance Dolibarr cible';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Configurez ici les paramètres de connexion à l\'instance Dolibarr vierge qui sera utilisée pour créer les démos.';
print '</div>';

// Formulaire de configuration
print '<form method="post" action="'.$_SERVER['PHP_SELF'].'">';
print '<input type="hidden" name="action" value="save">';
print '<input type="hidden" name="token" value="'.newToken().'">';

print '<table class="border centpercent">';

// Hôte de la base de données
print '<tr class="oddeven">';
print '<td class="fieldrequired">Hôte de la base de données</td>';
print '<td><input type="text" name="demo_db_host" value="'.dol_escape_htmltag($conf->global->RENDEZVOUSCLIENT_DEMO_DB_HOST ?? '').'" size="50" placeholder="localhost ou IP du serveur"></td>';
print '</tr>';

// Nom de la base de données
print '<tr class="oddeven">';
print '<td class="fieldrequired">Nom de la base de données</td>';
print '<td><input type="text" name="demo_db_name" value="'.dol_escape_htmltag($conf->global->RENDEZVOUSCLIENT_DEMO_DB_NAME ?? '').'" size="50" placeholder="dolibarr_demo"></td>';
print '</tr>';

// Utilisateur de la base de données
print '<tr class="oddeven">';
print '<td class="fieldrequired">Utilisateur de la base de données</td>';
print '<td><input type="text" name="demo_db_user" value="'.dol_escape_htmltag($conf->global->RENDEZVOUSCLIENT_DEMO_DB_USER ?? '').'" size="50" placeholder="root"></td>';
print '</tr>';

// Mot de passe de la base de données
print '<tr class="oddeven">';
print '<td>Mot de passe de la base de données</td>';
print '<td><input type="password" name="demo_db_password" value="'.dol_escape_htmltag($conf->global->RENDEZVOUSCLIENT_DEMO_DB_PASSWORD ?? '').'" size="50"></td>';
print '</tr>';

// Port de la base de données
print '<tr class="oddeven">';
print '<td>Port de la base de données</td>';
print '<td><input type="number" name="demo_db_port" value="'.dol_escape_htmltag($conf->global->RENDEZVOUSCLIENT_DEMO_DB_PORT ?? '3306').'" size="10" placeholder="3306"></td>';
print '</tr>';

// Préfixe des tables
print '<tr class="oddeven">';
print '<td>Préfixe des tables</td>';
print '<td><input type="text" name="demo_db_prefix" value="'.dol_escape_htmltag($conf->global->RENDEZVOUSCLIENT_DEMO_DB_PREFIX ?? 'llx_').'" size="20" placeholder="llx_"></td>';
print '</tr>';

// URL de l'instance Dolibarr
print '<tr class="oddeven">';
print '<td class="fieldrequired">URL de l\'instance Dolibarr</td>';
print '<td><input type="url" name="demo_url" value="'.dol_escape_htmltag($conf->global->RENDEZVOUSCLIENT_DEMO_URL ?? '').'" size="80" placeholder="https://demo.mondomaine.com"></td>';
print '</tr>';

print '</table>';

print '<div class="center">';
print '<input type="submit" class="button" value="Sauvegarder la configuration">';
print '</div>';

print '</form>';

// Actions de test
print '<br><h3>Tests et actions</h3>';

print '<div class="tabsAction">';
print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_connection&token='.newToken().'">Tester la connexion</a>';
print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=create_demo_tables&token='.newToken().'">Créer les tables de base</a>';
print '</div>';

// Statut de la configuration
print '<br><h3>Statut de la configuration</h3>';

$config_status = array();

$required_params = array(
    'RENDEZVOUSCLIENT_DEMO_DB_HOST' => 'Hôte de la base de données',
    'RENDEZVOUSCLIENT_DEMO_DB_NAME' => 'Nom de la base de données',
    'RENDEZVOUSCLIENT_DEMO_DB_USER' => 'Utilisateur de la base de données',
    'RENDEZVOUSCLIENT_DEMO_URL' => 'URL de l\'instance Dolibarr'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Paramètre</th>';
print '<th>Valeur</th>';
print '<th>Statut</th>';
print '</tr>';

$all_configured = true;

foreach ($required_params as $param => $label) {
    $value = $conf->global->$param ?? '';
    $configured = !empty($value);
    
    if (!$configured) {
        $all_configured = false;
    }
    
    print '<tr class="oddeven">';
    print '<td><strong>'.$label.'</strong></td>';
    print '<td>'.($configured ? dol_escape_htmltag($value) : '<em>Non configuré</em>').'</td>';
    print '<td>';
    if ($configured) {
        print '<span class="badge badge-status4 badge-status">✅ OK</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">❌ MANQUANT</span>';
    }
    print '</td>';
    print '</tr>';
}

print '</table>';

if ($all_configured) {
    print '<div class="ok">';
    print '<strong>✅ Configuration complète !</strong> Vous pouvez maintenant créer des démos qui configureront automatiquement l\'instance Dolibarr cible.';
    print '</div>';
} else {
    print '<div class="warning">';
    print '<strong>⚠️ Configuration incomplète</strong> - Veuillez remplir tous les paramètres requis.';
    print '</div>';
}

// Instructions
print '<br><h3>Instructions</h3>';

print '<div class="info">';
print '<h4>Comment configurer l\'instance Dolibarr cible :</h4>';
print '<ol>';
print '<li><strong>Préparez une installation Dolibarr vierge</strong> sur un serveur accessible</li>';
print '<li><strong>Créez une base de données</strong> dédiée pour les démos</li>';
print '<li><strong>Configurez les paramètres</strong> ci-dessus avec les informations de connexion</li>';
print '<li><strong>Testez la connexion</strong> pour vérifier que tout fonctionne</li>';
print '<li><strong>Créez les tables de base</strong> si nécessaire</li>';
print '</ol>';
print '</div>';

print '<div class="warning">';
print '<h4>⚠️ Sécurité :</h4>';
print '<ul>';
print '<li>Utilisez une base de données dédiée aux démos</li>';
print '<li>Configurez des accès restreints pour l\'utilisateur de base de données</li>';
print '<li>L\'instance cible sera réinitialisée à chaque création de démo</li>';
print '</ul>';
print '</div>';

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_demo_creation.php">Tester la création de démo</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/demo/manage.php">Gérer les démos</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
