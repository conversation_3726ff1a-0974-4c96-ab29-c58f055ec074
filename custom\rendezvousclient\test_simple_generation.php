<?php
/**
 *	\file       rendezvousclient/test_simple_generation.php
 *	\ingroup    rendezvousclient
 *	\brief      Test simple de génération de documents ODT
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

dol_include_once('/rendezvousclient/core/class/document_generator.class.php');
dol_include_once('/rendezvousclient/site/class/site.class.php');

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'create_template') {
    $templates_dir = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/';
    if (!is_dir($templates_dir)) {
        dol_mkdir($templates_dir);
    }
    
    $filename = $templates_dir . 'test_template.odt';
    
    // Contenu XML basique pour un document ODT
    $content_xml = '<?xml version="1.0" encoding="UTF-8"?>
<office:document-content xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0">
<office:body>
<office:text>
<text:h text:style-name="Heading_20_1" text:outline-level="1">Test de génération de document</text:h>
<text:p text:style-name="Standard"/>
<text:p text:style-name="Standard">Date de génération : __DATE_GENERATION__</text:p>
<text:p text:style-name="Standard">Généré par : __UTILISATEUR__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Client</text:h>
<text:p text:style-name="Standard">Société : __SOCIETE_NOM__</text:p>
<text:p text:style-name="Standard">Adresse : __SOCIETE_ADRESSE__</text:p>
<text:p text:style-name="Standard">__SOCIETE_CP__ __SOCIETE_VILLE__</text:p>
<text:p text:style-name="Standard">Téléphone : __SOCIETE_TEL__</text:p>
<text:p text:style-name="Standard">Email : __SOCIETE_EMAIL__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Informations Site</text:h>
<text:p text:style-name="Standard">Nom du site : __SITE_NOM__</text:p>
<text:p text:style-name="Standard">Type : __SITE_TYPE__</text:p>
<text:p text:style-name="Standard">Description : __SITE_DESCRIPTION__</text:p>
<text:p text:style-name="Standard">Nombre d\'utilisateurs : __SITE_NB_UTILISATEURS__</text:p>
<text:p text:style-name="Standard">Logiciel : __SITE_LOGICIEL__</text:p>
<text:p text:style-name="Standard">Hébergement : __SITE_HEBERGEMENT__</text:p>
<text:p text:style-name="Standard"/>

<text:h text:style-name="Heading_20_2" text:outline-level="2">Utilisateurs</text:h>
<text:p text:style-name="Standard">__SITE_UTILISATEURS__</text:p>

</office:text>
</office:body>
</office:document-content>';
    
    // Créer un fichier ZIP (ODT)
    $zip = new ZipArchive();
    if ($zip->open($filename, ZipArchive::CREATE) !== TRUE) {
        setEventMessages("Impossible de créer le fichier ODT", null, 'errors');
    } else {
        // Ajouter les fichiers nécessaires
        $zip->addFromString('mimetype', 'application/vnd.oasis.opendocument.text');
        $zip->addFromString('content.xml', $content_xml);
        
        // META-INF/manifest.xml
        $manifest = '<?xml version="1.0" encoding="UTF-8"?>
<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0">
<manifest:file-entry manifest:full-path="/" manifest:media-type="application/vnd.oasis.opendocument.text"/>
<manifest:file-entry manifest:full-path="content.xml" manifest:media-type="text/xml"/>
</manifest:manifest>';
        
        $zip->addFromString('META-INF/manifest.xml', $manifest);
        
        $zip->close();
        
        setEventMessages("Template de test créé avec succès", null, 'mesgs');
    }
}

if ($action == 'test_generation') {
    $site_id = GETPOST('site_id', 'int');
    
    if ($site_id > 0) {
        $site = new Site($db);
        $result = $site->fetch($site_id);
        
        if ($result > 0) {
            // Créer un template de test simple
            $templates_dir = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/';
            $test_template = $templates_dir . 'test_template.odt';
            
            if (!file_exists($test_template)) {
                setEventMessages("Template de test manquant. Créez-le d'abord.", null, 'errors');
            } else {
                try {
                    $generator = new DocumentGenerator($db);
                    
                    // Charger les données associées
                    $societe = new Societe($db);
                    $projet = new Project($db);
                    
                    if ($site->fk_projet > 0) {
                        $projet->fetch($site->fk_projet);
                        if ($projet->socid > 0) {
                            $societe->fetch($projet->socid);
                        }
                    }
                    
                    // Préparer les substitutions
                    $substitutions = $generator->prepareSubstitutions($site, $societe, $projet);
                    
                    // Créer le répertoire de sortie
                    $output_dir = DOL_DATA_ROOT . '/rendezvousclient/sites/' . $site->rowid . '/';
                    if (!is_dir($output_dir)) {
                        dol_mkdir($output_dir);
                    }
                    
                    // Nom du fichier de sortie
                    $filename = 'test_' . $site->nom . '_' . date('Y-m-d_H-i-s') . '.odt';
                    $output_file = $output_dir . $filename;
                    
                    // Copier le template
                    if (!copy($test_template, $output_file)) {
                        setEventMessages("Impossible de copier le template", null, 'errors');
                    } else {
                        // Traiter le fichier ODT
                        $zip = new ZipArchive();
                        
                        if ($zip->open($output_file) !== TRUE) {
                            setEventMessages("Impossible d'ouvrir le fichier ODT", null, 'errors');
                        } else {
                            // Lire le contenu principal (content.xml)
                            $content = $zip->getFromName('content.xml');
                            if ($content === false) {
                                setEventMessages("Impossible de lire content.xml", null, 'errors');
                            } else {
                                // Faire les substitutions
                                foreach ($substitutions as $search => $replace) {
                                    $content = str_replace($search, htmlspecialchars($replace, ENT_XML1), $content);
                                }
                                
                                // Réécrire le contenu
                                if (!$zip->deleteName('content.xml') || !$zip->addFromString('content.xml', $content)) {
                                    setEventMessages("Impossible de mettre à jour content.xml", null, 'errors');
                                } else {
                                    $zip->close();
                                    
                                    setEventMessages("Document généré avec succès: $filename", null, 'mesgs');
                                    
                                    // Proposer le téléchargement
                                    print '<div class="center">';
                                    print '<a href="'.DOL_URL_ROOT.'/document.php?modulepart=rendezvousclient&file=sites/'.$site->rowid.'/'.$filename.'" target="_blank" class="butAction">Télécharger le document généré</a>';
                                    print '</div>';
                                }
                            }
                        }
                    }
                    
                } catch (Exception $e) {
                    setEventMessages("Erreur: " . $e->getMessage(), null, 'errors');
                }
            }
        } else {
            setEventMessages("Site non trouvé", null, 'errors');
        }
    } else {
        setEventMessages("ID de site manquant", null, 'errors');
    }
}

/*
 * View
 */

$title = 'Test simple de génération de documents ODT';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script teste la génération de documents ODT de manière simplifiée.';
print '</div>';

// Vérifier les prérequis
print '<h3>Vérification des prérequis</h3>';

$prerequisites = array();

// Vérifier ZipArchive
$prerequisites[] = array(
    'test' => 'Extension ZipArchive',
    'status' => class_exists('ZipArchive') ? 'OK' : 'ERREUR',
    'details' => class_exists('ZipArchive') ? 'Extension disponible' : 'Extension ZipArchive non disponible'
);

// Vérifier le répertoire templates
$templates_dir = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/core/templates/';
$prerequisites[] = array(
    'test' => 'Répertoire templates',
    'status' => is_dir($templates_dir) ? 'OK' : 'ERREUR',
    'details' => is_dir($templates_dir) ? 'Répertoire existe' : 'Répertoire manquant'
);

// Vérifier le template de test
$test_template = $templates_dir . 'test_template.odt';
$prerequisites[] = array(
    'test' => 'Template de test',
    'status' => file_exists($test_template) ? 'OK' : 'ERREUR',
    'details' => file_exists($test_template) ? 'Template de test disponible' : 'Template de test manquant'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Test</th>';
print '<th>Statut</th>';
print '<th>Détails</th>';
print '</tr>';

foreach ($prerequisites as $prereq) {
    print '<tr class="oddeven">';
    print '<td><strong>'.$prereq['test'].'</strong></td>';
    print '<td>';
    if ($prereq['status'] == 'OK') {
        print '<span class="badge badge-status4 badge-status">OK</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">ERREUR</span>';
    }
    print '</td>';
    print '<td>'.$prereq['details'].'</td>';
    print '</tr>';
}

print '</table>';

// Bouton pour créer le template de test
if (!file_exists($test_template)) {
    print '<br><div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=create_template&token='.newToken().'">Créer le template de test</a>';
    print '</div>';
}

// Sélection du site pour test
print '<br><h3>Sélectionner un site pour tester</h3>';

$sql = "SELECT s.rowid, s.nom, soc.nom as nom_societe 
        FROM ".MAIN_DB_PREFIX."rendez_vous_site s
        LEFT JOIN ".MAIN_DB_PREFIX."projet p ON s.fk_projet = p.rowid
        LEFT JOIN ".MAIN_DB_PREFIX."societe soc ON p.socid = soc.rowid
        ORDER BY s.nom LIMIT 10";

$resql = $db->query($sql);

if ($resql && $db->num_rows($resql) > 0) {
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Site</th>';
    print '<th>Société</th>';
    print '<th>Action</th>';
    print '</tr>';
    
    while ($obj = $db->fetch_object($resql)) {
        print '<tr class="oddeven">';
        print '<td><strong>'.$obj->nom.'</strong></td>';
        print '<td>'.$obj->nom_societe.'</td>';
        print '<td>';
        if (file_exists($test_template)) {
            print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_generation&site_id='.$obj->rowid.'&token='.newToken().'">Tester génération</a>';
        } else {
            print '<span class="opacitymedium">Créez d\'abord le template</span>';
        }
        print '</td>';
        print '</tr>';
    }
    
    print '</table>';
} else {
    print '<div class="warning">Aucun site trouvé. Créez d\'abord un site pour tester la génération.</div>';
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/admin/templates.php">Gestion des templates</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Créer un site</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
