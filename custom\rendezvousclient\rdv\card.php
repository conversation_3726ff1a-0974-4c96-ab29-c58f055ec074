<?php
/**
 *	\file       rendezvousclient/rdv/card.php
 *	\ingroup    rendezvousclient
 *	\brief      fiche rendez vous
 */

require_once '../../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/societe/class/societe.class.php';
dol_include_once('/rendezvousclient/lib/rendezvousclient.lib.php');
dol_include_once('/rendezvousclient/rdv/class/rendezvous.class.php');
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/projet/class/project.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/doleditor.class.php';
require_once DOL_DOCUMENT_ROOT.'/contact/class/contact.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'users', 'companies'));

// id de la table rendez_vous
$id = GETPOST('id', 'int');
$action = GETPOST('action', 'aZ09');
$cancel	= GETPOST('cancel', 'alphanohtml');
$backtopage = GETPOST('backtopage', 'alpha');
$help_url = '';
$lastkey = ''; // Initialisation de la variable lastkey

// charge le rendezvous $id
$rendezvous = new Rendezvous($db);
if($id > 0){
    $rendezvous->fetch($id);
}

$projectstatic = new Project($db);
if($rendezvous->fk_projet > 0){
    $projectstatic->fetch($rendezvous->fk_projet);
    $rendezvous->socid = $projectstatic->socid;
}

$societe = new Societe($db);
if($projectstatic->socid > 0){
    $societe->fetch($projectstatic->socid);
}


/*
 * Actions
*/

if ($cancel && empty($id)){
    header("Location: ".DOL_URL_ROOT."/custom/rendezvousclient/list.php");
    exit;
}

if($action == 'setphone'){
    $phone = GETPOST('phone', 'alphanohtml');



    header("Location: ".DOL_URL_ROOT."/custom/rendezvousclient/rdv/card.php?id=".$id);
    exit;
}

if($action == 'setemail'){
    $email = GETPOST('email', 'alphanohtml');



    header("Location: ".DOL_URL_ROOT."/custom/rendezvousclient/rdv/card.php?id=".$id);
    exit;
}

if($action == 'setprojet'){
    $fk_projet = GETPOST('fk_projet', 'int');



    header("Location: ".DOL_URL_ROOT."/custom/rendezvousclient/rdv/card.php?id=".$id);
    exit;
}

if($action == 'add' && GETPOST('save') == $langs->trans('Save')){
    $error = 0;

    // Récupération des données du formulaire
    $rendezvous->fk_projet = GETPOST('fk_projet', 'int');
    $rendezvous->type = GETPOST('type', 'int');
    $rendezvous->numero = GETPOST('numero', 'alphanohtml');
    $rendezvous->objet = GETPOST('objet', 'alphanohtml');
    $rendezvous->fk_statut = GETPOST('fk_statut', 'int');
    $rendezvous->fk_user = GETPOST('fk_user', 'int');

    // Dates
    if (GETPOST('date')) {
        $rendezvous->date = dol_mktime(GETPOST('datehour', 'int'), GETPOST('datemin', 'int'), 0, GETPOST('datemonth', 'int'), GETPOST('dateday', 'int'), GETPOST('dateyear', 'int'));
    }
    if (GETPOST('date_demo')) {
        $rendezvous->date_demo = dol_mktime(GETPOST('date_demohour', 'int'), GETPOST('date_demomin', 'int'), 0, GETPOST('date_demomonth', 'int'), GETPOST('date_demoday', 'int'), GETPOST('date_demoyear', 'int'));
    }
    if (GETPOST('date_livraison')) {
        $rendezvous->date_livraison = dol_mktime(GETPOST('date_livraisonhour', 'int'), GETPOST('date_livraisonmin', 'int'), 0, GETPOST('date_livraisonmonth', 'int'), GETPOST('date_livraisonday', 'int'), GETPOST('date_livraisonyear', 'int'));
    }

    // Textes
    $rendezvous->objectif_client = GETPOST('objectif_client', 'restricthtml');
    $rendezvous->besoin_client = GETPOST('besoin_client', 'restricthtml');
    $rendezvous->reponse_besoin_client = GETPOST('reponse_besoin_client', 'restricthtml');
    $rendezvous->compte_rendu = GETPOST('compte_rendu', 'restricthtml');

    // Contacts
    $rendezvous->socpeople = GETPOST('socpeople', 'array:int');

    // Validation
    if (empty($rendezvous->fk_projet)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Project")), null, 'errors');
        $error++;
    }
    if (empty($rendezvous->objet)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("ObjRDV")), null, 'errors');
        $error++;
    }

    if (!$error) {
        $result = $rendezvous->create();
        if ($result > 0) {
            setEventMessages($langs->trans("RecordSaved"), null, 'mesgs');
            header("Location: ".DOL_URL_ROOT."/custom/rendezvousclient/rdv/card.php?id=".$result);
            exit;
        } else {
            setEventMessages($rendezvous->error, null, 'errors');
            $action = 'create';
        }
    } else {
        $action = 'create';
    }
}

if($action == 'update' && GETPOST('save') == $langs->trans('Save')){
    $error = 0;

    // Récupération des données du formulaire
    $rendezvous->type = GETPOST('type', 'int');
    $rendezvous->objet = GETPOST('objet', 'alphanohtml');
    $rendezvous->fk_statut = GETPOST('fk_statut', 'int');
    $rendezvous->fk_user = GETPOST('fk_user', 'int');

    // Dates
    if (GETPOST('date')) {
        $rendezvous->date = dol_mktime(GETPOST('datehour', 'int'), GETPOST('datemin', 'int'), 0, GETPOST('datemonth', 'int'), GETPOST('dateday', 'int'), GETPOST('dateyear', 'int'));
    }
    if (GETPOST('date_demo')) {
        $rendezvous->date_demo = dol_mktime(GETPOST('date_demohour', 'int'), GETPOST('date_demomin', 'int'), 0, GETPOST('date_demomonth', 'int'), GETPOST('date_demoday', 'int'), GETPOST('date_demoyear', 'int'));
    }
    if (GETPOST('date_livraison')) {
        $rendezvous->date_livraison = dol_mktime(GETPOST('date_livraisonhour', 'int'), GETPOST('date_livraisonmin', 'int'), 0, GETPOST('date_livraisonmonth', 'int'), GETPOST('date_livraisonday', 'int'), GETPOST('date_livraisonyear', 'int'));
    }

    // Textes
    $rendezvous->objectif_client = GETPOST('objectif_client', 'restricthtml');
    $rendezvous->besoin_client = GETPOST('besoin_client', 'restricthtml');
    $rendezvous->reponse_besoin_client = GETPOST('reponse_besoin_client', 'restricthtml');
    $rendezvous->compte_rendu = GETPOST('compte_rendu', 'restricthtml');

    // Contacts
    $rendezvous->socpeople = GETPOST('socpeople', 'array:int');

    // Validation
    if (empty($rendezvous->objet)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("ObjRDV")), null, 'errors');
        $error++;
    }

    if (!$error) {
        $result = $rendezvous->update();
        if ($result > 0) {
            setEventMessages($langs->trans("RecordSaved"), null, 'mesgs');
            header("Location: ".DOL_URL_ROOT."/custom/rendezvousclient/rdv/card.php?id=".$id);
            exit;
        } else {
            setEventMessages($rendezvous->error, null, 'errors');
            $action = 'edit';
        }
    } else {
        $action = 'edit';
    }
}

/*
 * View
*/

$form = new Form($db);
$formother = new FormOther($db);
$contactstatic = new Contact($db);

$title = $langs->trans('RDVlong');

llxHeader('', $title, $help_url);

if($action == 'create'){
    /*
     *  Creation
    */

    if(GETPOST('projectid', 'int')){
        // dans le cas où un projet est créer a partir de cette page
        $projecttmp = new Project($db);
        $projecttmp->fetch(GETPOST('projectid', 'int'));

        $_POST['fk_projet'] = GETPOST('projectid', 'int');
        $_POST['socid'] = $projecttmp->socid;
    }

    if(GETPOST('fk_projet', 'int')){
        $projecttmp = new Project($db);
        $projecttmp->fetch(GETPOST('fk_projet', 'int'));

        // quand on choisis un projet
        $res = $rendezvous->fetchLastRendezVous(GETPOST('fk_projet', 'int'));

        $lastkey = '';
        if($res > 0){
            // sera toujours 0 car il y a seulement 1 array dans $rendezvous->lastrdv
            $lastkey = 0;
            $numero = $rendezvous->getNextNum(GETPOST('fk_projet', 'int'));
        }
    }

    // quand choisis une societe depuis le formulaire de creation ou recupere la societe d'un projet créer a partir de cette page
    if(GETPOST('socid', 'int')){
        $societe->fetch(GETPOST('socid', 'int'));
    }


    $linkback = "";
    print load_fiche_titre($langs->trans("NewRDVClientlong"), $linkback, 'action');

    print '<form enctype="multipart/form-data" action="'.$_SERVER["PHP_SELF"].'" method="post" name="formrdv" autocomplete="off">'; // Chrome ignor autocomplete
    print '<input type="hidden" name="action" value="add">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="backtopage" value="'.$backtopage.'">';
    print dol_get_fiche_head(null, 'card', '', 0, '');

    print '<table class="border centpercent">';

    // numero
    print '<tr>';
    print '<td class="titlefieldcreate">'.$langs->trans('NumeroRDV').'</td>';
    print '<td>N°'.($numero ?? 'AUTO').'<input type="hidden" name="numero" value="'.($numero ?? '').'"></td>';
    print '</tr>';

    // type
    $listtype = array(
		1=>$langs->trans("ChezClient"),
		2=>$langs->trans("Appel"),
		3=>$langs->trans("Visio")
	);
    print '<tr>';
    print '<td class="fieldrequired titlefieldcreate">'.$langs->trans("Type").'</td>';
    print '<td>'.$form->selectarray('type', $listtype, (GETPOST('type', 'int') ? GETPOST('type', 'int') : ''), 0, 0, 0, '', 0, 0, 0, '', 'maxwidth125', 1).'</td>';
    print '</tr>';

    // Thirdparty
	print '<tr>';
	print '<td class="fieldrequired">'.$langs->trans('Customer').'</td>';
	if ($societe->id > 0) {
		if(!empty($conf->global->MAIN_AVIMM_CANCHANGESOC)){
			print '<td class="valuefieldcreate">';
			print img_picto('', 'company').$form->select_company($societe->id, 'socid', '', 'SelectThirdParty', 0, 0, null, 0, 'minwidth300 maxwidth500 widthcentpercentminusxx');
			// reload page to retrieve customer informations
			if (empty($conf->global->RELOAD_PAGE_ON_CUSTOMER_CHANGE_DISABLED)) {
				print '<script type="text/javascript">
				$(document).ready(function() {
					$("#socid").change(function() {
						console.log("We have changed the company - Reload page");
						var socid = $(this).val();
						// reload page
						$("input[name=action]").val("create");
						$("form[name=formrdv]").submit();
					});
				});
				</script>';
			}
			print '</td>';
		}else{
			print '<td>';
			print $societe->getNomUrl(1, 'customer');
			print '<input type="hidden" name="socid" value="'.$societe->id.'">';
			print '</td>';
		}
	} else {
		print '<td>';
		print img_picto('', 'company').$form->select_company('', 'socid', '', 'SelectThirdParty', 0, 0, null, 0, 'minwidth175 maxwidth500 widthcentpercentminusxx');
		// reload page to retrieve customer informations
		if (empty($conf->global->RELOAD_PAGE_ON_CUSTOMER_CHANGE_DISABLED)) {
			print '<script type="text/javascript">
			$(document).ready(function() {
				$("#socid").change(function() {
					console.log("We have changed the company - Reload page");
					var socid = $(this).val();
					// reload page
					$("input[name=action]").val("create");
					$("form[name=formrdv]").submit();
				});
			});
			</script>';
		}
		print ' <a href="'.DOL_URL_ROOT.'/societe/card.php?action=create&backtopage='.urlencode($_SERVER["PHP_SELF"].'?action=create').'"><span class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("AddThirdParty").'"></span></a>';
		print '</td>';
	}
	print '</tr>';

    if($societe->id){
        // contact
        $socpeople = '';
        if(GETPOST('socpeople', 'array:aZ09')){
            $socpeople = GETPOST('socpeople', 'array:aZ09');
        }
        if(is_numeric($lastkey) && isset($rendezvous->lastrdv[$lastkey]) && $rendezvous->lastrdv[$lastkey]->socpeople){
            $socpeople = $rendezvous->lastrdv[$lastkey]->socpeople;
        }
        print '<tr>';
        print '<td class="fieldrequired stitlefieldcreate">'.$langs->trans("ActionOnContact").'</td>';
        print '<td>'.$form->selectcontacts($societe->id, $socpeople, 'socpeople[]', 1, '', '', 1, 'quatrevingtpercent', false, 0, 0, array(), 'multiple', 'contactid').'</td>';
        print '</tr>';

        // projet
        $sql = "SELECT rowid, ref, title";
        $sql .= " FROM ".MAIN_DB_PREFIX."projet";
        $sql .= " WHERE fk_soc = ".$societe->id;
        $resql = $db->query($sql);

        print '<tr>';
        print '<td class="titlefieldcreate">'.$langs->trans('Project').'</td>';
        if($resql && $db->num_rows($resql) > 0){
            print '<td>';
            $arrayprojet = array();
            while($obj = $db->fetch_object($resql)){
                $arrayprojet[$obj->rowid] = $obj->ref.' - '.$obj->title;
            }
            print $form->selectarray('fk_projet', $arrayprojet, (GETPOST('fk_projet', 'int') ? GETPOST('fk_projet', 'int') : ''), 1);
            print '<script type="text/javascript">
			$(document).ready(function() {
				$("#fk_projet").change(function() {
					console.log("We have changed the company - Reload page");
					var fk_projet = $(this).val();
					// reload page
					$("input[name=action]").val("create");
					$("form[name=formrdv]").submit();
				});
			});
			</script>';
            print '</td>';
        }else{
            print '<td>Aucun projet pour ce client';
            print ' <a href="'.DOL_URL_ROOT.'/projet/card.php?action=create&socid='.$societe->id.'&backtopage='.urlencode($_SERVER["PHP_SELF"].'?action=create').'"><span class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("AddThirdParty").'"></span></a>';
            print '</td>';
        }
        print '</tr>';
    }

    // objet
    print '<tr>';
    print '<td class="titlefieldcreate fieldrequired">'.$langs->trans("ObjRDV").'</td>';
    print '<td><input type="text" size="50" maxlength="255" name="objet" value="'.(GETPOST('objet', 'alphanohtml') ? GETPOST('objet', 'alphanohtml') : '').'"></td>';
    print '</tr>';

    // date rdv
    $date = '';
    if(GETPOST('date')){
        $date = dol_mktime(GETPOST('datehour', 'int'), GETPOST('datemin', 'int'), 0, GETPOST('datemonth', 'int'), GETPOST('dateday', 'int'), GETPOST('dateyear', 'int'));
    }
    if(is_numeric($lastkey) && isset($rendezvous->lastrdv[$lastkey])){
        $date = strtotime($rendezvous->lastrdv[$lastkey]->date);
    }
    print '<tr>';
    print '<td class="fieldrequired stitlefieldcreate">'.$langs->trans("DateRDV").'</td>';
    print '<td>'.$form->selectDate($date, 'date', 1, 1, '', "formrdv", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
    print '</tr>';

    // date demo
    $date_demo = '';
    if(GETPOST('date_demo')){
        $date_demo = dol_mktime(GETPOST('date_demohour', 'int'), GETPOST('date_demomin', 'int'), 0, GETPOST('date_demomonth', 'int'), GETPOST('date_demoday', 'int'), GETPOST('date_demoyear', 'int'));
    }
    if(is_numeric($lastkey) && isset($rendezvous->lastrdv[$lastkey])){
        $date_demo = strtotime($rendezvous->lastrdv[$lastkey]->date_demo);
    }
    print '<tr>';
    print '<td class="titlefieldcreate">'.$langs->trans("DateD").'</td>';
    print '<td>'.$form->selectDate($date_demo, 'date_demo', 1, 1, 1, "formrdv", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
    print '</tr>';

    // date livraison
    $date_livraison = '';
    if(GETPOST('date_livraison')){
        $date_livraison = dol_mktime(GETPOST('date_livraisonhour', 'int'), GETPOST('date_livraisonmin', 'int'), 0, GETPOST('date_livraisonmonth', 'int'), GETPOST('date_livraisonday', 'int'), GETPOST('date_livraisonyear', 'int'));
    }
    if(is_numeric($lastkey) && isset($rendezvous->lastrdv[$lastkey])){
        $date_livraison = strtotime($rendezvous->lastrdv[$lastkey]->date_livraison);
    }
    print '<tr>';
    print '<td class="titlefieldcreate">'.$langs->trans("DATE_LIVRAISON").'</td>';
    print '<td>'.$form->selectDate($date_livraison, 'date_livraison', 1, 1, 1, "formrdv", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
    print '</tr>';

    // evenement assigne a
    print '<tr>';
    print '<td class="titlefieldcreate">'.$langs->trans('ActionAffectedTo').'</td>';
    print '<td>'.$form->select_dolusers((GETPOST('fk_user', 'int') ? GETPOST('fk_user', 'int') : $user->id), 'fk_user', 0);
    print '</tr>';

    // statut
    $liststatut = array(
		Rendezvous::STATUT_PLANIFIE=>$langs->trans("StatutPlanifie"),
		Rendezvous::STATUT_REALISE=>$langs->trans("StatutRealise"),
		Rendezvous::STATUT_ANNULE=>$langs->trans("StatutAnnule"),
		Rendezvous::STATUT_REPORTE=>$langs->trans("StatutReporte")
	);
    print '<tr>';
    print '<td class="titlefieldcreate fieldrequired">'.$langs->trans("Statut").'</td>';
    print '<td>'.$form->selectarray('fk_statut', $liststatut, (GETPOST('fk_statut', 'int') ? GETPOST('fk_statut', 'int') : ''), -1, 0, 0, '', 0, 0, 0, '', 'maxwidth125', 1).'</td>';
    print '</tr>';

    // Note prive
	print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('NotePrivateProjet').'</td>';
	print '<td>';
    $note_prive = '';
    if(GETPOST('note_prive', 'restricthtml')){
        $note_prive = GETPOST('note_prive', 'restricthtml');
    }
    if(isset($projecttmp) && $projecttmp->note_private){
        $note_prive = $projecttmp->note_private;
    }
	$doleditor = new DolEditor('note_prive', $note_prive, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    // objectifs client
	print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('ObjectifsClient').'</td>';
	print '<td>';
    $objectif_client = '';
    if(GETPOST('objectif_client', 'restricthtml')){
        $objectif_client = GETPOST('objectif_client', 'restricthtml');
    }
    if(is_numeric($lastkey) && isset($rendezvous->lastrdv[$lastkey])){
        $objectif_client = $rendezvous->lastrdv[$lastkey]->objectif_client;
    }
	$doleditor = new DolEditor('objectif_client', $objectif_client, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    // besoins client
	print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('BesoinsClient').'</td>';
	print '<td>';
    $besoin_client = '';
    if(GETPOST('besoin_client', 'restricthtml')){
        $besoin_client = GETPOST('besoin_client', 'restricthtml');
    }
    if(is_numeric($lastkey) && isset($rendezvous->lastrdv[$lastkey])){
        $besoin_client = $rendezvous->lastrdv[$lastkey]->besoin_client;
    }
	$doleditor = new DolEditor('besoin_client', $besoin_client, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    // reponses besoins client
	print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('ReponseBesoinClient').'</td>';
	print '<td>';
    $reponse_besoin_client = '';
    if(GETPOST('reponse_besoin_client', 'restricthtml')){
        $reponse_besoin_client = GETPOST('reponse_besoin_client', 'restricthtml');
    }
    if(is_numeric($lastkey) && isset($rendezvous->lastrdv[$lastkey])){
        $reponse_besoin_client = $rendezvous->lastrdv[$lastkey]->reponse_besoin_client;
    }
	$doleditor = new DolEditor('reponse_besoin_client', $reponse_besoin_client, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    // compte rendu
	print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('CompteRendu').'</td>';
	print '<td>';
    $compte_rendu = '';
    if(GETPOST('compte_rendu', 'restricthtml')){
        $compte_rendu = GETPOST('compte_rendu', 'restricthtml');
    }
    if(is_numeric($lastkey) && isset($rendezvous->lastrdv[$lastkey])){
        $compte_rendu = $rendezvous->lastrdv[$lastkey]->compte_rendu;
    }
	$doleditor = new DolEditor('compte_rendu', $compte_rendu, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '</table>';

    print dol_get_fiche_end();

    print $form->buttonsSaveCancel("Save");

	print '</form>';

}elseif($action == 'edit'){
    /*
     *  Edition
    */

    $head = rendezvousclient_prepare_head($rendezvous);

    print dol_get_fiche_head($head, 'card', $langs->trans("RDVlong"), -1, 'action');

    if(GETPOST('socid', 'int')){
        $societe->fetch(GETPOST('socid', 'int'));
    }

    print '
    <div class="arearef heightref valignmiddle centpercent">
        <!-- Start banner content -->
        <div style="vertical-align: middle">
            <div class="pagination paginationref">
                <ul class="right">
                </ul>
            </div>
            <!-- <div class="statusref">
                <span class="badge  badge-status4 badge-status" title="Ouvert">Ouvert</span>
            </div> -->
            <!-- morehtmlleft -->
            <div class="inline-block floatleft">
                <!-- No photo to show -->
                <div class="floatleft inline-block valignmiddle divphotoref">
                    <div class="photoref">
                        <!-- <span class="fas fa-project-diagram  em088 infobox-project" style="" title="No photo"></span> -->
                        '.img_picto('', 'action').'
                    </div>
                </div>
            </div>
            <div class="inline-block floatleft valignmiddle maxwidth750 marginbottomonly refid refidpadding">
                <div class="refidno">
                    ';
                    print $langs->trans('ThirdParty').' : '.$societe->getNomUrl(1, '');

                    print '<br/>'.$langs->trans('Phone').' : '.dol_print_phone($societe->phone, $societe->country_code, $contactid, $societe->id, 'AC_TEL', '&nbsp;', 'phone', $langs->trans("Phone"));

                    print '<br/>'.$langs->trans('Email').' : '.dol_print_email($societe->email, $societe->id, $societe->id, 'AC_EMAIL', 0, 0, 1);

                    print '<br/>'.$langs->trans('Project').' : '. $projectstatic->ref;
                    print '
                </div>
            </div>
        </div>
        <!-- End banner content -->
    </div>';
    print '<div class="underbanner clearboth"></div><br/>';

    print '<form enctype="multipart/form-data" action="'.$_SERVER["PHP_SELF"].'" method="post" name="formrdv" autocomplete="off">'; // Chrome ignor autocomplete
    print '<input type="hidden" name="action" value="update">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="id" value="'.$id.'">';

    print '<table class="border centpercent">';
    // numero
    print '<tr>';
    print '<td class="titlefieldcreate">'.$langs->trans('NumeroRDV').'</td>';
    print '<td>N°'.$rendezvous->numero.'</td>';
    print '</tr>';

    // type
    $listtype = array(
		1=>$langs->trans("ChezClient"),
		2=>$langs->trans("Appel"),
		3=>$langs->trans("Visio")
	);
    print '<tr>';
    print '<td class="fieldrequired titlefieldcreate">'.$langs->trans("Type").'</td>';
    print '<td>'.$form->selectarray('type', $listtype, (GETPOST('type', 'int') ? GETPOST('type', 'int') : $rendezvous->type), 0, 0, 0, '', 0, 0, 0, '', 'maxwidth125', 1).'</td>';
    print '</tr>';

    // Thirdparty
	print '<tr>';
	print '<td class="fieldrequired">'.$langs->trans('Customer').'</td>';
    print '<td>';
    print $societe->getNomUrl(1, 'customer');
    print '<input type="hidden" name="socid" value="'.$societe->id.'">';
    print '</td>';
	print '</tr>';

    // contact
    $socpeople = $rendezvous->socpeople;
    if(GETPOST('socpeople', 'array:aZ09')){
        $socpeople = GETPOST('socpeople', 'array:aZ09');
    }
    print '<tr>';
    print '<td class="fieldrequired stitlefieldcreate">'.$langs->trans("ActionOnContact").'</td>';
    print '<td>'.$form->selectcontacts($societe->id, $socpeople, 'socpeople[]', 1, '', '', 1, 'quatrevingtpercent', false, 0, 0, array(), 'multiple', 'contactid').'</td>';
    print '</tr>';

    // projet
    print '<tr>';
	print '<td class="fieldrequired">'.$langs->trans('Project').'</td>';
    print '<td>';
    print $projectstatic->getNomUrl(1);
    print '</td>';
	print '</tr>';

    // objet
    print '<tr>';
    print '<td class="titlefieldcreate fieldrequired">'.$langs->trans("ObjRDV").'</td>';
    print '<td><input type="text" size="50" maxlength="255" name="objet" value="'.(GETPOST('objet', 'alphanohtml') ? GETPOST('objet', 'alphanohtml') : $rendezvous->objet).'"></td>';
    print '</tr>';

    // date rdv
    $date = ($rendezvous->date ? strtotime($rendezvous->date) : '');
    if(GETPOST('date')){
        $date = dol_mktime(GETPOST('datehour', 'int'), GETPOST('datemin', 'int'), 0, GETPOST('datemonth', 'int'), GETPOST('dateday', 'int'), GETPOST('dateyear', 'int'));
    }
    print '<tr>';
    print '<td class="fieldrequired stitlefieldcreate">'.$langs->trans("DateRDV").'</td>';
    print '<td>'.$form->selectDate($date, 'date', 1, 1, '', "formrdv", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
    print '</tr>';

    // date demo
    $date_demo = ($rendezvous->date_demo ? strtotime($rendezvous->date_demo) : '');
    if(GETPOST('date_demo')){
        $date_demo = dol_mktime(GETPOST('date_demohour', 'int'), GETPOST('date_demomin', 'int'), 0, GETPOST('date_demomonth', 'int'), GETPOST('date_demoday', 'int'), GETPOST('date_demoyear', 'int'));
    }
    print '<tr>';
    print '<td class="fieldrequired stitlefieldcreate">'.$langs->trans("DateD").'</td>';
    print '<td>'.$form->selectDate($date_demo, 'date_demo', 1, 1, '', "formrdv", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
    print '</tr>';

    // date livraison
    $date_livraison = ($rendezvous->date_livraison ? strtotime($rendezvous->date_livraison) : '');
    if(GETPOST('date_livraison')){
        $date_livraison = dol_mktime(GETPOST('date_livraisonhour', 'int'), GETPOST('date_livraisonmin', 'int'), 0, GETPOST('date_livraisonmonth', 'int'), GETPOST('date_livraisonday', 'int'), GETPOST('date_livraisonyear', 'int'));
    }
    print '<tr>';
    print '<td class="fieldrequired stitlefieldcreate">'.$langs->trans("DATE_LIVRAISON").'</td>';
    print '<td>'.$form->selectDate($date_livraison, 'date_livraison', 1, 1, '', "formrdv", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
    print '</tr>';

    // evenement assigne a
    print '<tr>';
    print '<td class="titlefieldcreate">'.$langs->trans('ActionAffectedTo').'</td>';
    print '<td>'.$form->select_dolusers((GETPOST('fk_user', 'int') ? GETPOST('fk_user', 'int') : $rendezvous->fk_user), 'fk_user', 0);
    print '</tr>';

    // statut
    $liststatut = array(
		Rendezvous::STATUT_PLANIFIE=>$langs->trans("StatutPlanifie"),
		Rendezvous::STATUT_REALISE=>$langs->trans("StatutRealise"),
		Rendezvous::STATUT_ANNULE=>$langs->trans("StatutAnnule"),
		Rendezvous::STATUT_REPORTE=>$langs->trans("StatutReporte")
	);
    print '<tr>';
    print '<td class="titlefieldcreate fieldrequired">'.$langs->trans("Statut").'</td>';
    print '<td>'.$form->selectarray('fk_statut', $liststatut, (GETPOST('fk_statut', 'int') ? GETPOST('fk_statut', 'int') : $rendezvous->fk_statut), -1, 0, 0, '', 0, 0, 0, '', 'maxwidth125', 1).'</td>';
    print '</tr>';

    // Note prive
	print '<tr>';
	print '<td class="titlefieldcreate fieldrequired">'.$langs->trans('NotePrivateProjet').'</td>';
	print '<td>';
    $note_prive = $projectstatic->note_private;
    if(GETPOST('note_prive', 'restricthtml')){
        $note_prive = GETPOST('note_prive', 'restricthtml');
    }
	$doleditor = new DolEditor('note_prive', $note_prive, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    // objectifs client
	print '<tr>';
	print '<td class="titlefieldcreate fieldrequired">'.$langs->trans('ObjectifsClient').'</td>';
	print '<td>';
    $objectif_client = $rendezvous->objectif_client;
    if(GETPOST('objectif_client', 'restricthtml')){
        $objectif_client = GETPOST('objectif_client', 'restricthtml');
    }
	$doleditor = new DolEditor('objectif_client', $objectif_client, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    // besoins client
	print '<tr>';
	print '<td class="titlefieldcreate fieldrequired">'.$langs->trans('BesoinsClient').'</td>';
	print '<td>';
    $besoin_client = $rendezvous->besoin_client;
    if(GETPOST('besoin_client', 'restricthtml')){
        $besoin_client = GETPOST('besoin_client', 'restricthtml');
    }
	$doleditor = new DolEditor('besoin_client', $besoin_client, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    // reponses besoins client
	print '<tr>';
	print '<td class="titlefieldcreate fieldrequired">'.$langs->trans('ReponseBesoinClient').'</td>';
	print '<td>';
    $reponse_besoin_client = $rendezvous->reponse_besoin_client;
    if(GETPOST('reponse_besoin_client', 'restricthtml')){
        $reponse_besoin_client = GETPOST('reponse_besoin_client', 'restricthtml');
    }
	$doleditor = new DolEditor('reponse_besoin_client', $reponse_besoin_client, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    // compte rendu
	print '<tr>';
	print '<td class="titlefieldcreate fieldrequired">'.$langs->trans('CompteRendu').'</td>';
	print '<td>';
    $compte_rendu = $rendezvous->compte_rendu;
    if(GETPOST('compte_rendu', 'restricthtml')){
        $compte_rendu = GETPOST('compte_rendu', 'restricthtml');
    }
	$doleditor = new DolEditor('compte_rendu', $compte_rendu, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '</table>';

    print dol_get_fiche_end();

    print $form->buttonsSaveCancel("Save");

	print '</form>';

}else{
    /*
     *  View
    */
    $head = rendezvousclient_prepare_head($rendezvous);

    print dol_get_fiche_head($head, 'card', $langs->trans("RDVlong"), -1, 'action');

    print '
    <div class="arearef heightref valignmiddle centpercent">
        <!-- Start banner content -->
        <div style="vertical-align: middle">
            <div class="pagination paginationref">
                <ul class="right">
                    <li class="noborder litext clearbothonsmartphone">
                        <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rdv/list.php">Retour liste</a>
                    </li>
                </ul>
            </div>
            <!-- morehtmlleft -->
            <div class="inline-block floatleft">
                <!-- No photo to show -->
                <div class="floatleft inline-block valignmiddle divphotoref">
                    <div class="photoref">
                        <!-- <span class="fas fa-project-diagram  em088 infobox-project" style="" title="No photo"></span> -->
                        '.img_picto('', 'action').'
                    </div>
                </div>
            </div>
            <div class="inline-block floatleft valignmiddle maxwidth750 marginbottomonly refid refidpadding">
                <div class="refidno">
                    ';
                    print $langs->trans('ThirdParty').' : '.$societe->getNomUrl(1, '');

                    print '<br/>'.$langs->trans('Phone').' ';
                    if($action != 'editphone'){
                        print '<a class="editfielda" href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=editphone">'.img_edit().'</a> : ';
                        print dol_print_phone($societe->phone, $societe->country_code, $contactid, $societe->id, 'AC_TEL', '&nbsp;', 'phone', $langs->trans("Phone"));
                    }else{
                        print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
                        print '<input type="hidden" name="action" value="setphone">';
                        print '<input type="hidden" name="token" value="'.newToken().'">';
                        print '<input type="text" name="phone" value="'.$societe->phone.'">';
                        print '<input type="submit" class="button valignmiddle" value="'.$langs->trans("Modify").'">';
                        print '</form>';
                    }

                    print '<br/>'.$langs->trans('Email').' ';
                    if($action != 'editemail'){
                        print '<a class="editfielda" href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=editemail">'.img_edit().'</a> : ';
                        print dol_print_email($societe->email, $societe->id, $societe->id, 'AC_EMAIL', 0, 0, 1);
                    }else{
                        print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
                        print '<input type="hidden" name="action" value="setemail">';
                        print '<input type="hidden" name="token" value="'.newToken().'">';
                        print '<input type="text" name="email" value="'.$societe->email.'">';
                        print '<input type="submit" class="button valignmiddle" value="'.$langs->trans("Modify").'">';
                        print '</form>';
                    }

                    print '<br/>'.$langs->trans('Project').' ';
                    if($action != 'editprojet'){
                        print '<a class="editfielda" href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=editprojet">'.img_edit().'</a> : ';
                        print $projectstatic->getNomUrl(1);
                    }else{
                        print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
                        print '<input type="hidden" name="action" value="setprojet">';
                        print '<input type="hidden" name="token" value="'.newToken().'">';
                        print $form->selectProjects($projectstatic->id, 'fk_projet');
                        print '<input type="submit" class="button valignmiddle" value="'.$langs->trans("Modify").'">';
                        print '</form>';
                    }
                    print '
                </div>
            </div>
        </div>
        <!-- End banner content -->
    </div>';

    print '<div class="fichecenter">';

    print '<div class="fichehalfleft">';
    print '<div class="underbanner clearboth"></div>';

    print '<table class="border tableforfield centpercent">';

    // nombre de rendez vous
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("NbRDV").'</td>';
    print '<td>'.$rendezvous->getNbRendezVous().'</td>';
    print '</tr>';

    // numero du rendez vous
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("NumeroRDV").'</td>';
    print '<td>'.$rendezvous->numero.'</td>';
    print '</tr>';

    // type
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("Type").'</td>';
    print '<td>'.$rendezvous->getLibType($rendezvous->type).'</td>';
    print '</tr>';

    // contact
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("ActionOnContact").'</td>';
    print '<td>';
    if(count($rendezvous->socpeople) > 0){
        foreach($rendezvous->socpeople as $contact){
            $contactstatic->fetch($contact);
            print $contactstatic->getNomUrl(-1);
        }
    }else{
        print 'Aucun contact';
    }
    print '</td>';
    print '</tr>';

    // projet
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("Project").'</td>';
    print '<td>';
    if($rendezvous->fk_projet > 0){
        $projectstatic->fetch($rendezvous->fk_projet);
        print $projectstatic->getNomUrl(-1);
    }else{
        print 'Aucun projet';
    }
    print '</td>';
    print '</tr>';

    // objet du rendez vous
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("ObjRDV").'</td>';
    print '<td>'.$rendezvous->objet.'</td>';
    print '</tr>';

    // date
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("DateRDV").'</td>';
    print '<td>'.date('d/m/Y H:i:s', strtotime($rendezvous->date)).'</td>';
    print '</tr>';

    // date demo
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("DateD").'</td>';
    print '<td>'.date('d/m/Y H:i:s', strtotime($rendezvous->date_demo)).'</td>';
    print '</tr>';

    // date livraison
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("DATE_LIVRAISON").'</td>';
    print '<td>'.date('d/m/Y H:i:s', strtotime($rendezvous->date_livraison)).'</td>';
    print '</tr>';

    // assigne a
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("ActionAffectedTo").'</td>';
	$userstatic = new User($db);
    $userstatic->fetch($rendezvous->fk_user);
    print '<td>'.$userstatic->getNomUrl(-1).'</td>';
    print '</tr>';

    // statut
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("Statut").'</td>';
    print '<td>'.$rendezvous->getLibStatut($rendezvous->fk_statut).'</td>';
    print '</tr>';

    // note privee
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("NotePrivateProjet").'</td>';
    print '<td>'.dol_htmlentitiesbr($projectstatic->note_private).'</td>';
    print '</tr>';

    // objectifs client
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("ObjectifsClient").'</td>';
    print '<td>'.dol_htmlentitiesbr($rendezvous->objectif_client).'</td>';
    print '</tr>';

    // besoins client
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("BesoinsClient").'</td>';
    print '<td>'.dol_htmlentitiesbr($rendezvous->besoin_client).'</td>';
    print '</tr>';

    // reponses besoins client
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("ReponseBesoinClient").'</td>';
    print '<td>'.dol_htmlentitiesbr($rendezvous->reponse_besoin_client).'</td>';
    print '</tr>';

    // compte rendu
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("CompteRendu").'</td>';
    print '<td>'.dol_htmlentitiesbr($rendezvous->compte_rendu).'</td>';
    print '</tr>';

    print '</table>';

    // FIN div fichehalfleft
    print '</div>';

    print '<div class="fichehalfright">';
    print '<div class="underbanner clearboth"></div>';

    print 'Suivi rendez-vous client <br/>';
    print '<table class="liste border centpercent">';

    print '<tr class="liste_titre">';
    print '<td style="width: 11%">'.$langs->trans('NumeroRDVshort').'</td>';
    print '<td style="width: 21%">'.$langs->trans("DateRDV").'</td>';
    print '<td style="width: 21%">'.$langs->trans("Type").'</td>';
    print '<td style="width: 21%">'.$langs->trans("Statut").'</td>';
    print '<td style="width: 21%">'.$langs->trans("ObjRDV").'</td>';
    print '<td style="width: 5%"></td>';
    print '</tr>';

    $rendezvous->getOtherRDV();
    if(count($rendezvous->otherrdv) > 0){
        // compte le nombre de ligne affiché, si toujours 0 (dans le cas la societe n'a que 1 rendez vous qui est celui qui est deja afficher sur cette page, aucune ligne ne sera afficher)
        $ligne = 0;

        foreach($rendezvous->otherrdv as $line){
            if($line->rowid != $rendezvous->rowid && ($line->fk_statut == 1 || $line->fk_statut == 4)){
                $ligne++;

	            print "<tr onclick=\"window.location='card.php?id=".$line->rowid."&save_lastsearch_values=1'\" class=\"oddeven\">";
                print '<td>'.$line->numero.'</td>';
                print '<td>'.date('d/m/Y H:i:s', strtotime($line->date)).'</td>';
                print '<td>'.$rendezvous->getLibType($line->type).'</td>';
                print '<td>'.$rendezvous->getLibStatut($line->fk_statut).'</td>';
                print '<td>'.$line->objet.'</td>';
                print '<td>'.img_picto($langs->trans('View'), 'preview').'</td>';
                print '</tr>';
            }
        }

        if($ligne == 0){
            print '<tr class="oddeven">';
            print '<td colspan="6">Aucun autre rendez-vous</td>';
            print '</tr>';
        }
    }else{
        print '<tr class="oddeven">';
        print '<td colspan="6">Aucun autre rendez-vous</td>';
        print '</tr>';
    }

    print '</table>';

    // FIN div fichehalfright
    print '</div>';

    // FIN div fichecenter
    print '</div>';

    print dol_get_fiche_end();


    print '<div class="clearboth"></div><br>';

    print '<div class="tabsAction">'."\n";

    print dolGetButtonAction('', $langs->trans('createsite'), 'default', $_SERVER["PHP_SELF"].'?id='.$rendezvous->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('createcahiercharge'), 'default', $_SERVER["PHP_SELF"].'?id='.$rendezvous->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('createdevis'), 'default', $_SERVER["PHP_SELF"].'?id='.$rendezvous->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('Modify'), 'default', $_SERVER["PHP_SELF"].'?id='.$rendezvous->rowid.'&action=edit&token='.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('Delete'), 'default', $_SERVER["PHP_SELF"].'?id='.$rendezvous->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('SendMail'), 'default', $_SERVER["PHP_SELF"].'?id='.$rendezvous->rowid.'#'.newToken(), '', 1);

    print '</div>';

}


// End of page
llxFooter();
$db->close();
